# Composer dependencies
/vendor/
composer.phar

# Log files
/logs/*.log
/logs/*.txt

# User uploads and generated content
/uploads/billboards/*
/uploads/thumbnails/*
# Keep the directories but ignore contents
!/uploads/billboards/.gitkeep
!/uploads/thumbnails/.gitkeep

# Temporary files
/tmp/
/temp/
*.tmp

# Configuration files with sensitive data
# Uncomment these if you have sensitive config files
# /config/database.php
# /config/email.php
# /config/payment.php

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# PHP specific
*.log
.env
.env.local
.env.*.local

# Cache files
*.cache
/cache/

# Session files
/sessions/

# Backup files
*.bak
*.backup
*~

# Node.js (if you add any frontend build tools later)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build artifacts
/build/
/dist/
