# InfinityFree Deployment Guide

## Step-by-Step Setup Instructions

### 1. Database Setup
1. Go to your InfinityFree control panel
2. Click on "MySQL Databases"
3. Create a new database (note down the details):
   - Database name: `epiz_XXXXXXXX_billboard`
   - Username: `epiz_XXXXXXXX`
   - Password: (your chosen password)
   - Host: `sql123.epizy.com` (or similar)

### 2. Update Database Configuration
1. Open `config/database.php`
2. Replace the database credentials with your InfinityFree details:
   ```php
   define('DB_HOST', 'sql123.epizy.com'); // Your actual host
   define('DB_USERNAME', 'epiz_XXXXXXXX'); // Your actual username
   define('DB_PASSWORD', 'your_password'); // Your actual password
   define('DB_NAME', 'epiz_XXXXXXXX_billboard'); // Your actual database name
   ```

### 3. Import Database Structure
1. Go to your InfinityFree control panel
2. Click on "phpMyAdmin"
3. Select your database
4. Click "Import" tab
5. Upload the `setup_database.sql` file
6. Click "Go" to execute

### 4. Upload Files
1. Go to your InfinityFree control panel
2. Click on "File Manager" or use FTP
3. Navigate to `htdocs` folder
4. Upload ALL project files to this folder
5. Make sure the folder structure is preserved:
   ```
   htdocs/
   ├── administrator/
   ├── customer/
   ├── assets/
   ├── config/
   ├── logs/
   ├── stock-image/
   ├── uploads/
   ├── vendor/
   └── index.php (if you have one)
   ```

### 5. Set Folder Permissions
1. In File Manager, right-click on these folders:
   - `logs/`
   - `uploads/`
   - `uploads/billboards/`
   - `uploads/thumbnails/`
2. Set permissions to 755 or 777

### 6. Test Your Installation
1. Visit your website: `https://yoursubdomain.epizy.com`
2. Test admin login: `https://yoursubdomain.epizy.com/administrator/`
   - Username: `admin`
   - Password: `admin123`
3. Test customer interface: `https://yoursubdomain.epizy.com/customer/`

### 7. Important Notes
- InfinityFree has some limitations on file uploads and execution time
- Large image processing might timeout
- Email functions might need configuration
- Some PHP functions might be restricted

### 8. Troubleshooting
- If you get database connection errors, double-check your credentials
- If pages don't load, check file permissions
- If uploads don't work, check folder permissions
- Check InfinityFree's error logs in the control panel

### Default Login Credentials
- **Admin Username:** admin
- **Admin Password:** admin123
- **Admin Email:** <EMAIL>

**Remember to change these credentials after testing!**
