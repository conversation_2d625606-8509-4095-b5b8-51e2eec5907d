<?php
// Debug script to check financial dashboard issues
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Financial Dashboard Debug</h1>";

try {
    require_once 'includes/auth.php';
    echo "<p>✅ Auth included successfully</p>";
    
    requireAdminLogin();
    echo "<p>✅ Admin login check passed</p>";
    
    require_once dirname(__DIR__) . '/config/database.php';
    echo "<p>✅ Database config included</p>";
    
    $pdo = getDBConnection();
    echo "<p>✅ Database connection successful</p>";
    
    // Check if orders table exists and has expected columns
    $stmt = $pdo->query("DESCRIBE orders");
    $columns = $stmt->fetchAll();
    echo "<h3>Orders table columns:</h3><ul>";
    foreach ($columns as $column) {
        echo "<li>" . $column['Field'] . " (" . $column['Type'] . ")</li>";
    }
    echo "</ul>";
    
    // Check if we have any orders
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders");
    $count = $stmt->fetchColumn();
    echo "<p>Total orders in database: $count</p>";
    
    // Check if we have any paid orders
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders WHERE status = 'paid'");
    $paid_count = $stmt->fetchColumn();
    echo "<p>Paid orders: $paid_count</p>";
    
    // Check payment_completed_at column
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders WHERE payment_completed_at IS NOT NULL");
    $completed_count = $stmt->fetchColumn();
    echo "<p>Orders with payment_completed_at: $completed_count</p>";
    
    // Try to include financial functions
    require_once 'includes/financial-functions.php';
    echo "<p>✅ Financial functions included</p>";
    
    // Test getAllRevenuePeriods function
    $revenue_data = getAllRevenuePeriods();
    echo "<h3>Revenue data:</h3>";
    echo "<pre>" . print_r($revenue_data, true) . "</pre>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
