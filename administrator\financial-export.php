<?php
require_once 'includes/auth.php';
requireAdminLogin();

require_once dirname(__DIR__) . '/config/database.php';
require_once 'includes/financial-functions.php';

$current_admin = getCurrentAdmin();

// Get filter parameters from URL
$filters = [
    'start_date' => $_GET['start_date'] ?? '',
    'end_date' => $_GET['end_date'] ?? '',
    'billboard_type' => $_GET['billboard_type'] ?? '',
    'customer_search' => $_GET['customer_search'] ?? '',
    'min_amount' => $_GET['min_amount'] ?? '',
    'max_amount' => $_GET['max_amount'] ?? ''
];

// Remove empty filters
$active_filters = array_filter($filters, function($value) {
    return $value !== '' && $value !== null;
});

// Get all transaction data (no pagination for export)
$transaction_data = getTransactionLog($active_filters, 1, 10000); // Large limit for export
$summary_data = getTransactionSummary($active_filters);

// Generate filename
$filename = 'financial_report_' . date('Y-m-d_H-i-s') . '.pdf';

// For now, we'll create an HTML version that can be printed to PDF
// This is a simple solution that works without additional libraries
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Report - <?php echo date('M j, Y'); ?></title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
            .page-break { page-break-before: always; }
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            color: #333;
        }
        
        .header p {
            margin: 5px 0;
            color: #666;
        }
        
        .summary-section {
            margin-bottom: 30px;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .summary-card {
            border: 1px solid #ddd;
            padding: 15px;
            text-align: center;
        }
        
        .summary-card h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #666;
            text-transform: uppercase;
        }
        
        .summary-value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        
        .filters-applied {
            background: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #667eea;
            margin-bottom: 20px;
        }
        
        .filters-applied h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
        }
        
        .filter-item {
            margin: 5px 0;
        }
        
        .transactions-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .transactions-table th,
        .transactions-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .transactions-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .transactions-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        
        .amount-cell {
            text-align: right;
            font-weight: bold;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }
        
        .export-actions {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .btn {
            padding: 10px 20px;
            margin: 0 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <!-- Export Actions (hidden when printing) -->
    <div class="export-actions no-print">
        <button onclick="window.print()" class="btn btn-primary">Print / Save as PDF</button>
        <a href="financial-reports.php" class="btn btn-secondary">Back to Reports</a>
    </div>

    <!-- Report Header -->
    <div class="header">
        <h1>Borges Media Billboard</h1>
        <h2>Financial Report</h2>
        <p>Generated on <?php echo date('F j, Y \a\t g:i A'); ?></p>
        <p>Report prepared by: <?php echo htmlspecialchars($current_admin['username']); ?></p>
    </div>

    <!-- Applied Filters -->
    <?php if (!empty($active_filters)): ?>
        <div class="filters-applied">
            <h3>Applied Filters</h3>
            <?php foreach ($active_filters as $key => $value): ?>
                <div class="filter-item">
                    <strong><?php echo ucwords(str_replace('_', ' ', $key)); ?>:</strong> 
                    <?php echo htmlspecialchars($value); ?>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>

    <!-- Summary Section -->
    <div class="summary-section">
        <h3>Summary Statistics</h3>
        <div class="summary-grid">
            <div class="summary-card">
                <h3>Total Transactions</h3>
                <div class="summary-value"><?php echo number_format($summary_data['total_transactions']); ?></div>
            </div>
            <div class="summary-card">
                <h3>Total Revenue</h3>
                <div class="summary-value"><?php echo formatCurrency($summary_data['total_revenue']); ?></div>
            </div>
            <div class="summary-card">
                <h3>Average Amount</h3>
                <div class="summary-value"><?php echo formatCurrency($summary_data['average_amount']); ?></div>
            </div>
        </div>
        
        <?php if ($summary_data['first_transaction'] && $summary_data['last_transaction']): ?>
            <p><strong>Date Range:</strong> 
                <?php echo date('M j, Y', strtotime($summary_data['first_transaction'])); ?> - 
                <?php echo date('M j, Y', strtotime($summary_data['last_transaction'])); ?>
            </p>
        <?php endif; ?>
    </div>

    <!-- Transaction Details -->
    <div class="transactions-section">
        <h3>Transaction Details</h3>
        
        <?php if (!empty($transaction_data['transactions'])): ?>
            <table class="transactions-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Order #</th>
                        <th>Customer</th>
                        <th>Email</th>
                        <th>Type</th>
                        <th>Duration</th>
                        <th>Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($transaction_data['transactions'] as $transaction): ?>
                        <tr>
                            <td><?php echo date('M j, Y', strtotime($transaction['payment_date'])); ?></td>
                            <td><?php echo htmlspecialchars($transaction['order_number']); ?></td>
                            <td><?php echo htmlspecialchars($transaction['customer_name']); ?></td>
                            <td><?php echo htmlspecialchars($transaction['customer_email']); ?></td>
                            <td><?php echo ucfirst(htmlspecialchars($transaction['billboard_type'])); ?></td>
                            <td><?php echo $transaction['booking_duration_days']; ?> days</td>
                            <td class="amount-cell"><?php echo formatCurrency($transaction['total_amount']); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else: ?>
            <p>No transactions found for the specified criteria.</p>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p>This report contains <?php echo count($transaction_data['transactions']); ?> transactions.</p>
        <p>Borges Media Billboard Admin System - Confidential</p>
    </div>

    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() { window.print(); };
    </script>
</body>
</html>
