<?php
require_once 'includes/auth.php';
requireAdminLogin();

require_once dirname(__DIR__) . '/config/database.php';
require_once 'includes/financial-functions.php';

$current_admin = getCurrentAdmin();

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: index.php');
    exit();
}

// Get filter parameters
$filters = [
    'start_date' => $_GET['start_date'] ?? '',
    'end_date' => $_GET['end_date'] ?? '',
    'billboard_type' => $_GET['billboard_type'] ?? '',
    'customer_search' => $_GET['customer_search'] ?? '',
    'min_amount' => $_GET['min_amount'] ?? '',
    'max_amount' => $_GET['max_amount'] ?? ''
];

// Get pagination parameters
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page_options = [10, 25, 50, 100];
$per_page = (int)($_GET['per_page'] ?? 25);
if (!in_array($per_page, $per_page_options)) {
    $per_page = 25;
}

// Remove empty filters
$active_filters = array_filter($filters, function($value) {
    return $value !== '' && $value !== null;
});

// Get revenue data for all periods (for the cards at the top)
try {
    $revenue_data = getAllRevenuePeriods();
} catch (Exception $e) {
    $revenue_data = [
        'today' => ['total_revenue' => 0, 'transaction_count' => 0, 'period_label' => 'Today', 'error' => $e->getMessage()],
        'week' => ['total_revenue' => 0, 'transaction_count' => 0, 'period_label' => 'This Week', 'error' => $e->getMessage()],
        'month' => ['total_revenue' => 0, 'transaction_count' => 0, 'period_label' => 'This Month', 'error' => $e->getMessage()],
        'year' => ['total_revenue' => 0, 'transaction_count' => 0, 'period_label' => 'This Year', 'error' => $e->getMessage()]
    ];
    $error_message = 'Error loading revenue data: ' . $e->getMessage();
}

// Get transaction data
try {
    $transaction_data = getTransactionLog($active_filters, $page, $per_page);
    $summary_data = getTransactionSummary($active_filters);
} catch (Exception $e) {
    $transaction_data = [
        'transactions' => [],
        'total_count' => 0,
        'total_pages' => 0,
        'current_page' => 1,
        'per_page' => $per_page,
        'has_next' => false,
        'has_prev' => false,
        'error' => $e->getMessage()
    ];
    $summary_data = [
        'total_transactions' => 0,
        'total_revenue' => 0,
        'average_amount' => 0,
        'error' => $e->getMessage()
    ];
    $error_message = 'Error loading transaction data: ' . $e->getMessage();
}

// Handle PDF export
if (isset($_GET['export']) && $_GET['export'] === 'pdf') {
    // We'll implement this in the next step
    header('Location: financial-export.php?' . http_build_query($_GET));
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Dashboard - Borges Media Admin</title>
    <link rel="stylesheet" href="../assets/css/admin-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Financial Reports Specific Styles */
        .revenue-cards {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 25px;
        }

        .revenue-card {
            background: white;
            padding: 16px 20px;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-left: 3px solid #667eea;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .revenue-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .revenue-card h3 {
            color: #6b7280;
            font-size: 11px;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            font-weight: 600;
        }

        .revenue-amount {
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 4px;
            line-height: 1;
        }

        .revenue-count {
            color: #9ca3af;
            font-size: 12px;
            font-weight: 500;
        }

        /* Responsive revenue cards */
        @media (max-width: 1200px) {
            .revenue-cards {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }
        }

        @media (max-width: 768px) {
            .revenue-cards {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .revenue-card {
                padding: 14px 16px;
            }

            .revenue-amount {
                font-size: 20px;
            }
        }

        /* Proper Filter Layout - Following Industry Best Practices */

        /* Quick Filters Bar - Always visible at top */
        .quick-filters-bar {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px 8px 0 0;
            padding: 12px 20px;
            margin-bottom: 0;
        }

        .quick-filters-content {
            display: flex;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;
        }

        .quick-filters-label {
            font-size: 13px;
            font-weight: 600;
            color: #6b7280;
            display: flex;
            align-items: center;
            gap: 6px;
            white-space: nowrap;
        }

        .quick-filter-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .quick-filter-btn {
            padding: 6px 12px;
            font-size: 12px;
            border: 1px solid #d1d5db;
            background: white;
            color: #374151;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .quick-filter-btn:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }

        .quick-filter-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        /* Main Filter Bar - Horizontal layout */
        .main-filter-bar {
            background: white;
            border: 1px solid #e5e7eb;
            border-top: none;
            border-radius: 0 0 8px 8px;
            padding: 16px 20px;
            margin-bottom: 20px;
        }

        .filter-bar-content {
            display: flex;
            align-items: end;
            gap: 16px;
            flex-wrap: wrap;
        }

        .primary-filters {
            display: flex;
            align-items: end;
            gap: 16px;
            flex: 1;
            flex-wrap: wrap;
        }

        .filter-item {
            display: flex;
            flex-direction: column;
            min-width: 120px;
        }

        .filter-item label {
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 4px;
        }

        .filter-item input,
        .filter-item select {
            padding: 8px 10px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 13px;
            background: white;
            transition: all 0.2s ease;
        }

        .filter-item input:focus,
        .filter-item select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }

        /* Date and Amount Input Groups */
        .date-inputs,
        .amount-inputs {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .date-inputs input,
        .amount-inputs input {
            flex: 1;
            min-width: 80px;
        }

        .date-separator,
        .amount-separator {
            color: #6b7280;
            font-size: 12px;
            font-weight: 500;
        }

        /* Search input special styling */
        .filter-item input[name="customer_search"] {
            min-width: 200px;
        }

        /* Filter Actions Inline */
        .filter-actions-inline {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .btn-apply,
        .btn-clear,
        .btn-export {
            padding: 8px 12px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            text-decoration: none;
            border: none;
        }

        .btn-apply {
            background: #667eea;
            color: white;
        }

        .btn-apply:hover {
            background: #5a67d8;
        }

        .btn-clear {
            background: #f9fafb;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        .btn-clear:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .btn-export {
            background: #10b981;
            color: white;
        }

        .btn-export:hover {
            background: #059669;
        }

        /* Applied Filters Display */
        .applied-filters-display {
            background: #fffbeb;
            border: 1px solid #fbbf24;
            border-radius: 6px;
            padding: 12px 16px;
            margin-bottom: 16px;
        }

        .applied-filters-content {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        .applied-filters-label {
            font-size: 12px;
            font-weight: 600;
            color: #92400e;
        }

        .applied-filter-tags {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
            flex: 1;
        }

        .applied-filter-tag {
            background: #fbbf24;
            color: #92400e;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .applied-filter-tag .remove {
            cursor: pointer;
            opacity: 0.8;
            transition: opacity 0.2s ease;
            font-weight: bold;
        }

        .applied-filter-tag .remove:hover {
            opacity: 1;
        }

        .clear-all-btn {
            background: #dc2626;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .clear-all-btn:hover {
            background: #b91c1c;
        }
        
        /* Filter Actions - Sticky bottom bar */
        .filter-actions {
            background: white;
            border-top: 1px solid #e5e7eb;
            padding: 16px 20px;
            display: flex;
            gap: 8px;
            justify-content: space-between;
            align-items: center;
        }

        .filter-actions-left {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .filter-actions-right {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .filter-actions .btn {
            padding: 10px 16px;
            font-size: 13px;
            font-weight: 500;
            border-radius: 6px;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            text-decoration: none;
        }

        .filter-actions .btn-primary {
            background: #667eea;
            color: white;
        }

        .filter-actions .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .filter-actions .btn-secondary {
            background: #f9fafb;
            border: 1px solid #d1d5db;
            color: #374151;
        }

        .filter-actions .btn-secondary:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }

        .filter-actions .btn-success {
            background: #10b981;
            color: white;
        }

        .filter-actions .btn-success:hover {
            background: #059669;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .filter-actions .btn-ghost {
            background: transparent;
            color: #6b7280;
            border: 1px solid transparent;
        }

        .filter-actions .btn-ghost:hover {
            background: #f3f4f6;
            color: #374151;
        }

        /* Applied Filters Display */
        .applied-filters {
            background: #f8fafc;
            border-bottom: 1px solid #e5e7eb;
            padding: 12px 20px;
            display: none;
        }

        .applied-filters.has-filters {
            display: block;
        }

        .applied-filters-label {
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            margin-bottom: 8px;
        }

        .applied-filter-tags {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }

        .applied-filter-tag {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .applied-filter-tag .remove {
            cursor: pointer;
            opacity: 0.8;
            transition: opacity 0.2s ease;
        }

        .applied-filter-tag .remove:hover {
            opacity: 1;
        }

        /* Responsive Design for Mobile - Horizontal Filter Layout */
        @media (max-width: 1024px) {
            .filter-bar-content {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }

            .primary-filters {
                flex-direction: column;
                gap: 12px;
            }

            .filter-item {
                min-width: auto;
            }

            .filter-item input[name="customer_search"] {
                min-width: auto;
            }

            .filter-actions-inline {
                justify-content: center;
            }
        }

        @media (max-width: 768px) {
            .quick-filters-bar {
                padding: 10px 16px;
            }

            .quick-filters-content {
                flex-direction: column;
                align-items: stretch;
                gap: 8px;
            }

            .quick-filter-buttons {
                justify-content: center;
                gap: 6px;
            }

            .quick-filter-btn {
                flex: 1;
                min-width: 0;
                font-size: 11px;
                padding: 5px 8px;
            }

            .main-filter-bar {
                padding: 12px 16px;
            }

            .date-inputs,
            .amount-inputs {
                flex-direction: column;
                gap: 4px;
            }

            .date-separator,
            .amount-separator {
                display: none;
            }

            .filter-actions-inline {
                flex-direction: column;
                gap: 8px;
            }

            .btn-apply,
            .btn-clear,
            .btn-export {
                justify-content: center;
                width: 100%;
            }

            .applied-filters-display {
                padding: 8px 12px;
            }

            .applied-filters-content {
                flex-direction: column;
                align-items: stretch;
                gap: 8px;
            }
        }

        @media (max-width: 480px) {
            .quick-filter-buttons {
                flex-direction: column;
            }

            .quick-filter-btn {
                text-align: center;
            }

            .primary-filters {
                gap: 8px;
            }
        }
        
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }

        .summary-card {
            background: white;
            padding: 16px;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            text-align: center;
            border: 1px solid #e5e7eb;
        }

        .summary-card h4 {
            color: #6b7280;
            font-size: 11px;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            font-weight: 600;
        }

        .summary-value {
            font-size: 20px;
            font-weight: 700;
            color: #1f2937;
        }

        @media (max-width: 768px) {
            .summary-cards {
                grid-template-columns: 1fr;
                gap: 8px;
            }
        }
        
        .reports-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e5e7eb;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            border-bottom: 1px solid #f3f4f6;
            padding-bottom: 12px;
        }

        .section-header h3 {
            font-size: 16px;
            color: #374151;
            font-weight: 600;
            margin: 0;
        }

        .section-header select {
            padding: 6px 10px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 12px;
            background: white;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }
        
        .pagination a,
        .pagination span {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-decoration: none;
            color: #333;
        }
        
        .pagination .current {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .pagination a:hover {
            background: #f8f9fa;
        }
        
        .no-results {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }

        .no-results i {
            font-size: 48px;
            color: #d1d5db;
            margin-bottom: 16px;
            display: block;
        }

        .no-results h3 {
            color: #374151;
            margin-bottom: 8px;
            font-size: 18px;
        }

        .no-results p {
            color: #6b7280;
            font-size: 14px;
        }

        /* Enhanced table styling */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }

        .data-table th {
            background: #f9fafb;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .data-table td {
            padding: 12px 8px;
            border-bottom: 1px solid #f3f4f6;
            color: #374151;
        }

        .data-table tr:hover {
            background: #f9fafb;
        }

        /* Compact spacing for main content */
        .main-content h2 {
            margin-bottom: 20px;
            color: #1f2937;
            font-size: 24px;
            font-weight: 700;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 4px;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s;
            display: inline-block;
        }

        .btn-secondary-small {
            background: #6c757d;
            color: white;
        }

        .btn-secondary-small:hover {
            background: #5a6268;
        }

        .type-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .type-custom {
            background: #e3f2fd;
            color: #1976d2;
        }

        .type-templated {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .amount-cell {
            font-weight: bold;
            color: #28a745;
            text-align: right;
        }
    </style>
</head>
<body<?php echo (isset($_COOKIE['sidebarCollapsed']) && $_COOKIE['sidebarCollapsed'] === 'true') ? ' class="sidebar-collapsed"' : ''; ?>>
    <div class="admin-layout">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-content">
                <div class="header-brand">
                    <img src="../assets/images/bm-header-logo.png" alt="Borges Media Logo" class="admin-logo">
                    <h1>Borges Media Billboard Admin</h1>
                </div>
                <div class="header-user">
                    Welcome, <?php echo htmlspecialchars($current_admin['username']); ?>
                </div>
            </div>
        </header>
        
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Main Content -->
        <main class="admin-main">
            <div class="main-content">
                <h2>Financial Dashboard</h2>

                <!-- Revenue Cards -->
                <div class="revenue-cards">
                    <?php foreach ($revenue_data as $period => $data): ?>
                        <div class="revenue-card">
                            <h3><?php echo htmlspecialchars($data['period_label']); ?></h3>
                            <div class="revenue-amount"><?php echo formatCurrency($data['total_revenue']); ?></div>
                            <div class="revenue-count"><?php echo $data['transaction_count']; ?> transactions</div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Proper Filter Layout - Following Industry Best Practices -->

                <!-- Quick Date Presets Bar - Most Important Filters First -->
                <div class="quick-filters-bar">
                    <div class="quick-filters-content">
                        <div class="quick-filters-label">
                            <i class="fas fa-clock"></i> Quick Filters:
                        </div>
                        <div class="quick-filter-buttons">
                            <button type="button" onclick="setDateRange('today')" class="quick-filter-btn">Today</button>
                            <button type="button" onclick="setDateRange('week')" class="quick-filter-btn">This Week</button>
                            <button type="button" onclick="setDateRange('month')" class="quick-filter-btn">This Month</button>
                            <button type="button" onclick="setDateRange('year')" class="quick-filter-btn">This Year</button>
                            <button type="button" onclick="setDateRange('last30')" class="quick-filter-btn">Last 30 Days</button>
                        </div>
                    </div>
                </div>

                <!-- Main Filter Bar - Horizontal Layout -->
                <div class="main-filter-bar">
                    <form method="GET" class="filter-form">
                        <div class="filter-bar-content">
                            <!-- Primary Filters - Always Visible -->
                            <div class="primary-filters">
                                <div class="filter-item">
                                    <label>Date Range</label>
                                    <div class="date-inputs">
                                        <input type="date" name="start_date" id="start_date" value="<?php echo htmlspecialchars($filters['start_date']); ?>" placeholder="From">
                                        <span class="date-separator">to</span>
                                        <input type="date" name="end_date" id="end_date" value="<?php echo htmlspecialchars($filters['end_date']); ?>" placeholder="To">
                                    </div>
                                </div>

                                <div class="filter-item">
                                    <label>Search</label>
                                    <input type="text" name="customer_search" id="customer_search" placeholder="Customer name, email, or order #" value="<?php echo htmlspecialchars($filters['customer_search']); ?>">
                                </div>

                                <div class="filter-item">
                                    <label>Type</label>
                                    <select name="billboard_type" id="billboard_type">
                                        <option value="">All Types</option>
                                        <option value="custom" <?php echo $filters['billboard_type'] === 'custom' ? 'selected' : ''; ?>>Custom</option>
                                        <option value="templated" <?php echo $filters['billboard_type'] === 'templated' ? 'selected' : ''; ?>>Template</option>
                                    </select>
                                </div>

                                <div class="filter-item">
                                    <label>Amount Range</label>
                                    <div class="amount-inputs">
                                        <input type="number" name="min_amount" id="min_amount" step="0.01" placeholder="Min" value="<?php echo htmlspecialchars($filters['min_amount']); ?>">
                                        <span class="amount-separator">-</span>
                                        <input type="number" name="max_amount" id="max_amount" step="0.01" placeholder="Max" value="<?php echo htmlspecialchars($filters['max_amount']); ?>">
                                    </div>
                                </div>
                            </div>

                            <!-- Filter Actions -->
                            <div class="filter-actions-inline">
                                <button type="submit" class="btn-apply">
                                    <i class="fas fa-search"></i> Apply
                                </button>
                                <button type="button" onclick="clearAllFilters()" class="btn-clear">
                                    <i class="fas fa-times"></i> Clear
                                </button>
                                <?php if (!empty($active_filters)): ?>
                                    <a href="?export=pdf&<?php echo http_build_query($active_filters); ?>" class="btn-export">
                                        <i class="fas fa-file-pdf"></i> Export
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Applied Filters Display -->
                <div class="applied-filters-display" id="applied-filters-display" style="display: none;">
                    <div class="applied-filters-content">
                        <span class="applied-filters-label">Active Filters:</span>
                        <div class="applied-filter-tags" id="applied-filter-tags"></div>
                        <button type="button" onclick="clearAllFilters()" class="clear-all-btn">Clear All</button>
                    </div>
                </div>


                <!-- Summary Cards -->
                <?php if (!empty($active_filters)): ?>
                    <div class="summary-cards">
                        <div class="summary-card">
                            <h4>Total Transactions</h4>
                            <div class="summary-value"><?php echo number_format($summary_data['total_transactions']); ?></div>
                        </div>
                        <div class="summary-card">
                            <h4>Total Revenue</h4>
                            <div class="summary-value"><?php echo formatCurrency($summary_data['total_revenue']); ?></div>
                        </div>
                        <div class="summary-card">
                            <h4>Average Amount</h4>
                            <div class="summary-value"><?php echo formatCurrency($summary_data['average_amount']); ?></div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Transaction Table -->
                <div class="reports-section">
                    <div class="section-header">
                        <h3>Transaction Log</h3>
                        <div>
                            <select name="per_page" onchange="updatePerPage(this.value)">
                                <?php foreach ($per_page_options as $option): ?>
                                    <option value="<?php echo $option; ?>" <?php echo $per_page === $option ? 'selected' : ''; ?>>
                                        <?php echo $option; ?> per page
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <?php if (!empty($transaction_data['transactions'])): ?>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Order #</th>
                                    <th>Customer</th>
                                    <th>Email</th>
                                    <th>Type</th>
                                    <th>Amount</th>
                                    <th>Duration</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($transaction_data['transactions'] as $transaction): ?>
                                    <tr>
                                        <td><?php echo date('M j, Y', strtotime($transaction['payment_date'])); ?></td>
                                        <td><?php echo htmlspecialchars($transaction['order_number']); ?></td>
                                        <td><?php echo htmlspecialchars($transaction['customer_name']); ?></td>
                                        <td><?php echo htmlspecialchars($transaction['customer_email']); ?></td>
                                        <td>
                                            <span class="type-badge type-<?php echo htmlspecialchars($transaction['billboard_type']); ?>">
                                                <?php echo ucfirst(htmlspecialchars($transaction['billboard_type'])); ?>
                                            </span>
                                        </td>
                                        <td class="amount-cell"><?php echo formatCurrency($transaction['total_amount']); ?></td>
                                        <td><?php echo $transaction['booking_duration_days']; ?> days</td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        
                        <!-- Pagination -->
                        <?php if ($transaction_data['total_pages'] > 1): ?>
                            <div class="pagination">
                                <?php if ($transaction_data['has_prev']): ?>
                                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                        <i class="fas fa-chevron-left"></i> Previous
                                    </a>
                                <?php endif; ?>
                                
                                <span class="current">
                                    Page <?php echo $page; ?> of <?php echo $transaction_data['total_pages']; ?>
                                </span>
                                
                                <?php if ($transaction_data['has_next']): ?>
                                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                        Next <i class="fas fa-chevron-right"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="no-results">
                            <i class="fas fa-search" style="font-size: 48px; color: #ccc; margin-bottom: 20px;"></i>
                            <h3>No transactions found</h3>
                            <p>Try adjusting your filters to see more results.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Enhanced Filter UI JavaScript - Following UX Best Practices

        function updatePerPage(value) {
            const url = new URL(window.location);
            url.searchParams.set('per_page', value);
            url.searchParams.set('page', '1'); // Reset to first page
            window.location.href = url.toString();
        }

        // Toggle filter panel collapse/expand
        function toggleFilters() {
            const content = document.getElementById('filter-content');
            const icon = document.getElementById('filter-toggle-icon');

            if (content.style.display === 'none') {
                content.style.display = 'block';
                icon.className = 'fas fa-chevron-up';
            } else {
                content.style.display = 'none';
                icon.className = 'fas fa-chevron-down';
            }
        }

        // Quick date range presets with visual feedback
        function setDateRange(preset) {
            const today = new Date();
            const startDateInput = document.getElementById('start_date');
            const endDateInput = document.getElementById('end_date');

            // Remove active class from all buttons
            document.querySelectorAll('.quick-filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Add active class to clicked button
            event.target.classList.add('active');

            let startDate, endDate;

            switch(preset) {
                case 'today':
                    startDate = endDate = today;
                    break;
                case 'week':
                    const startOfWeek = new Date(today);
                    startOfWeek.setDate(today.getDate() - today.getDay() + 1); // Monday
                    startDate = startOfWeek;
                    endDate = today;
                    break;
                case 'month':
                    startDate = new Date(today.getFullYear(), today.getMonth(), 1);
                    endDate = today;
                    break;
                case 'year':
                    startDate = new Date(today.getFullYear(), 0, 1);
                    endDate = today;
                    break;
                case 'last30':
                    startDate = new Date(today);
                    startDate.setDate(today.getDate() - 30);
                    endDate = today;
                    break;
            }

            startDateInput.value = startDate.toISOString().split('T')[0];
            endDateInput.value = endDate.toISOString().split('T')[0];

            // Update applied filters display
            updateAppliedFilters();

            // Auto-submit the form for immediate results
            document.querySelector('.filter-form').submit();
        }

        // Clear all filters
        function clearAllFilters() {
            // Clear all form inputs
            document.getElementById('start_date').value = '';
            document.getElementById('end_date').value = '';
            document.getElementById('customer_search').value = '';
            document.getElementById('billboard_type').value = '';
            document.getElementById('min_amount').value = '';
            document.getElementById('max_amount').value = '';

            // Remove active class from quick filter buttons
            document.querySelectorAll('.quick-filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Update applied filters display
            updateAppliedFilters();

            // Redirect to clear URL parameters
            window.location.href = 'financial-reports.php';
        }

        // Update applied filters display
        function updateAppliedFilters() {
            const appliedFiltersContainer = document.getElementById('applied-filters-display');
            const appliedFilterTags = document.getElementById('applied-filter-tags');
            const filters = [];

            // Check each filter field
            const startDate = document.getElementById('start_date').value;
            const endDate = document.getElementById('end_date').value;
            const customerSearch = document.getElementById('customer_search').value;
            const billboardType = document.getElementById('billboard_type').value;
            const minAmount = document.getElementById('min_amount').value;
            const maxAmount = document.getElementById('max_amount').value;

            if (startDate && endDate) {
                filters.push(`Date: ${startDate} to ${endDate}`);
            } else if (startDate) {
                filters.push(`From: ${startDate}`);
            } else if (endDate) {
                filters.push(`Until: ${endDate}`);
            }

            if (customerSearch) {
                filters.push(`Search: ${customerSearch}`);
            }

            if (billboardType) {
                const typeText = billboardType === 'custom' ? 'Custom' : 'Template';
                filters.push(`Type: ${typeText}`);
            }

            if (minAmount && maxAmount) {
                filters.push(`Amount: $${minAmount} - $${maxAmount}`);
            } else if (minAmount) {
                filters.push(`Min: $${minAmount}`);
            } else if (maxAmount) {
                filters.push(`Max: $${maxAmount}`);
            }

            // Update display
            if (filters.length > 0) {
                appliedFiltersContainer.style.display = 'block';
                appliedFilterTags.innerHTML = filters.map(filter =>
                    `<span class="applied-filter-tag">${filter} <span class="remove" onclick="removeFilter('${filter}')">×</span></span>`
                ).join('');
            } else {
                appliedFiltersContainer.style.display = 'none';
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            const startDate = document.getElementById('start_date');
            const endDate = document.getElementById('end_date');

            // Validate date range
            function validateDateRange() {
                if (startDate.value && endDate.value) {
                    if (new Date(startDate.value) > new Date(endDate.value)) {
                        alert('Start date cannot be after end date');
                        startDate.focus();
                        return false;
                    }
                }
                return true;
            }

            // Add event listeners
            startDate.addEventListener('change', function() {
                validateDateRange();
                updateAppliedFilters();
            });

            endDate.addEventListener('change', function() {
                validateDateRange();
                updateAppliedFilters();
            });

            // Add listeners to other filter fields
            document.getElementById('customer_search').addEventListener('input', updateAppliedFilters);
            document.getElementById('billboard_type').addEventListener('change', updateAppliedFilters);
            document.getElementById('min_amount').addEventListener('input', updateAppliedFilters);
            document.getElementById('max_amount').addEventListener('input', updateAppliedFilters);

            // Initialize applied filters display
            updateAppliedFilters();
        });
    </script>
</body>
</html>
