<?php
/**
 * Financial Functions for Billboard Admin Dashboard
 * Provides revenue tracking and financial analytics functionality
 */

require_once dirname(__DIR__, 2) . '/config/database.php';

/**
 * Get revenue for a specific time period
 * 
 * @param string $period - 'today', 'week', 'month', 'year'
 * @return array - Contains total_revenue, transaction_count, and period_label
 */
function getRevenuePeriod($period) {
    try {
        $pdo = getDBConnection();
        
        // Define date ranges based on period
        switch ($period) {
            case 'today':
                $start_date = date('Y-m-d 00:00:00');
                $end_date = date('Y-m-d 23:59:59');
                $period_label = 'Today';
                break;
                
            case 'week':
                $start_date = date('Y-m-d 00:00:00', strtotime('monday this week'));
                $end_date = date('Y-m-d 23:59:59', strtotime('sunday this week'));
                $period_label = 'This Week';
                break;
                
            case 'month':
                $start_date = date('Y-m-01 00:00:00');
                $end_date = date('Y-m-t 23:59:59');
                $period_label = 'This Month';
                break;
                
            case 'year':
                $start_date = date('Y-01-01 00:00:00');
                $end_date = date('Y-12-31 23:59:59');
                $period_label = 'This Year';
                break;
                
            default:
                throw new InvalidArgumentException('Invalid period specified');
        }
        
        // Query to get revenue data - check if payment_completed_at exists, fallback to created_at
        $check_column = $pdo->query("SHOW COLUMNS FROM orders LIKE 'payment_completed_at'");
        $date_column = $check_column->rowCount() > 0 ? 'payment_completed_at' : 'created_at';

        // Check what status columns exist
        $status_check = $pdo->query("SHOW COLUMNS FROM orders LIKE 'payment_status'");
        $has_payment_status = $status_check->rowCount() > 0;

        $where_clause = "o.status = 'paid'";
        if ($has_payment_status) {
            $where_clause = "o.payment_status = 'completed' AND o.status = 'paid'";
        }

        $stmt = $pdo->prepare("
            SELECT
                COALESCE(SUM(o.total_amount), 0) as total_revenue,
                COUNT(o.id) as transaction_count
            FROM orders o
            WHERE $where_clause
            AND o.$date_column BETWEEN ? AND ?
        ");
        
        $stmt->execute([$start_date, $end_date]);
        $result = $stmt->fetch();
        
        return [
            'total_revenue' => (float)$result['total_revenue'],
            'transaction_count' => (int)$result['transaction_count'],
            'period_label' => $period_label,
            'start_date' => $start_date,
            'end_date' => $end_date
        ];
        
    } catch (Exception $e) {
        error_log("Error getting revenue for period $period: " . $e->getMessage());
        return [
            'total_revenue' => 0,
            'transaction_count' => 0,
            'period_label' => ucfirst($period),
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Get all revenue periods for dashboard display
 * 
 * @return array - Array of revenue data for all periods
 */
function getAllRevenuePeriods() {
    $periods = ['today', 'week', 'month', 'year'];
    $revenue_data = [];
    
    foreach ($periods as $period) {
        $revenue_data[$period] = getRevenuePeriod($period);
    }
    
    return $revenue_data;
}

/**
 * Get revenue for custom date range
 * 
 * @param string $start_date - Start date (Y-m-d format)
 * @param string $end_date - End date (Y-m-d format)
 * @return array - Revenue data for the specified range
 */
function getRevenueCustomRange($start_date, $end_date) {
    try {
        $pdo = getDBConnection();
        
        // Validate dates
        if (!validateDate($start_date) || !validateDate($end_date)) {
            throw new InvalidArgumentException('Invalid date format');
        }
        
        // Ensure start date is not after end date
        if (strtotime($start_date) > strtotime($end_date)) {
            throw new InvalidArgumentException('Start date cannot be after end date');
        }
        
        // Add time components for full day coverage
        $start_datetime = $start_date . ' 00:00:00';
        $end_datetime = $end_date . ' 23:59:59';
        
        // Check if payment_completed_at exists, fallback to created_at
        $check_column = $pdo->query("SHOW COLUMNS FROM orders LIKE 'payment_completed_at'");
        $date_column = $check_column->rowCount() > 0 ? 'payment_completed_at' : 'created_at';

        // Check what status columns exist
        $status_check = $pdo->query("SHOW COLUMNS FROM orders LIKE 'payment_status'");
        $has_payment_status = $status_check->rowCount() > 0;

        $where_clause = "o.status = 'paid'";
        if ($has_payment_status) {
            $where_clause = "o.payment_status = 'completed' AND o.status = 'paid'";
        }

        $stmt = $pdo->prepare("
            SELECT
                COALESCE(SUM(o.total_amount), 0) as total_revenue,
                COUNT(o.id) as transaction_count
            FROM orders o
            WHERE $where_clause
            AND o.$date_column BETWEEN ? AND ?
        ");
        
        $stmt->execute([$start_datetime, $end_datetime]);
        $result = $stmt->fetch();
        
        return [
            'total_revenue' => (float)$result['total_revenue'],
            'transaction_count' => (int)$result['transaction_count'],
            'period_label' => formatDateRange($start_date, $end_date),
            'start_date' => $start_datetime,
            'end_date' => $end_datetime
        ];
        
    } catch (Exception $e) {
        error_log("Error getting custom range revenue: " . $e->getMessage());
        return [
            'total_revenue' => 0,
            'transaction_count' => 0,
            'period_label' => 'Custom Range',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Validate date format (Y-m-d)
 * 
 * @param string $date
 * @return bool
 */
function validateDate($date) {
    $d = DateTime::createFromFormat('Y-m-d', $date);
    return $d && $d->format('Y-m-d') === $date;
}

/**
 * Format date range for display
 * 
 * @param string $start_date
 * @param string $end_date
 * @return string
 */
function formatDateRange($start_date, $end_date) {
    $start = date('M j, Y', strtotime($start_date));
    $end = date('M j, Y', strtotime($end_date));
    
    if ($start_date === $end_date) {
        return $start;
    }
    
    return "$start - $end";
}

/**
 * Format currency for display
 *
 * @param float $amount
 * @param string $currency
 * @return string
 */
function formatCurrency($amount, $currency = 'USD') {
    return '$' . number_format($amount, 2);
}

/**
 * Get transaction log with filtering and pagination
 *
 * @param array $filters - Array of filter parameters
 * @param int $page - Page number for pagination
 * @param int $per_page - Number of records per page
 * @return array - Contains transactions, total_count, and pagination info
 */
function getTransactionLog($filters = [], $page = 1, $per_page = 25) {
    try {
        $pdo = getDBConnection();

        // Check if payment_completed_at exists, fallback to created_at
        $check_column = $pdo->query("SHOW COLUMNS FROM orders LIKE 'payment_completed_at'");
        $date_column = $check_column->rowCount() > 0 ? 'payment_completed_at' : 'created_at';

        // Build WHERE clause based on filters - check for different status column combinations
        $where_conditions = [];
        $params = [];

        // Check what status columns exist and build appropriate conditions
        $status_check = $pdo->query("SHOW COLUMNS FROM orders LIKE 'payment_status'");
        $has_payment_status = $status_check->rowCount() > 0;

        if ($has_payment_status) {
            $where_conditions[] = "o.payment_status = 'completed'";
        }

        // Always check for status = 'paid' if it exists
        $where_conditions[] = "o.status = 'paid'";

        // If no specific conditions, just get all orders (for testing)
        if (empty($where_conditions)) {
            $where_conditions[] = "1 = 1";
        }

        // Date range filter
        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            $where_conditions[] = "DATE(o.$date_column) BETWEEN ? AND ?";
            $params[] = $filters['start_date'];
            $params[] = $filters['end_date'];
        }

        // Billboard type filter
        if (!empty($filters['billboard_type'])) {
            $where_conditions[] = "o.billboard_type = ?";
            $params[] = $filters['billboard_type'];
        }

        // Customer search filter
        if (!empty($filters['customer_search'])) {
            $where_conditions[] = "(o.customer_name LIKE ? OR o.customer_email LIKE ? OR o.order_number LIKE ?)";
            $search_term = '%' . $filters['customer_search'] . '%';
            $params[] = $search_term;
            $params[] = $search_term;
            $params[] = $search_term;
        }

        // Amount range filter
        if (!empty($filters['min_amount'])) {
            $where_conditions[] = "o.total_amount >= ?";
            $params[] = (float)$filters['min_amount'];
        }

        if (!empty($filters['max_amount'])) {
            $where_conditions[] = "o.total_amount <= ?";
            $params[] = (float)$filters['max_amount'];
        }

        $where_clause = implode(' AND ', $where_conditions);

        // Get total count for pagination
        $count_query = "
            SELECT COUNT(*) as total_count
            FROM orders o
            WHERE $where_clause
        ";

        $stmt = $pdo->prepare($count_query);
        $stmt->execute($params);
        $total_count = $stmt->fetchColumn();

        // Calculate pagination
        $total_pages = ceil($total_count / $per_page);
        $offset = ($page - 1) * $per_page;

        // Get transactions with pagination
        $query = "
            SELECT
                o.id,
                o.order_number,
                o.customer_name,
                o.customer_email,
                o.billboard_type,
                o.total_amount,
                o.$date_column as payment_completed_at,
                DATE(o.$date_column) as payment_date,
                o.booking_start_date,
                o.booking_end_date,
                o.booking_duration_days
            FROM orders o
            WHERE $where_clause
            ORDER BY o.$date_column DESC
            LIMIT ? OFFSET ?
        ";

        $params[] = $per_page;
        $params[] = $offset;

        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        $transactions = $stmt->fetchAll();

        return [
            'transactions' => $transactions,
            'total_count' => $total_count,
            'total_pages' => $total_pages,
            'current_page' => $page,
            'per_page' => $per_page,
            'has_next' => $page < $total_pages,
            'has_prev' => $page > 1
        ];

    } catch (Exception $e) {
        error_log("Error getting transaction log: " . $e->getMessage());
        return [
            'transactions' => [],
            'total_count' => 0,
            'total_pages' => 0,
            'current_page' => 1,
            'per_page' => $per_page,
            'has_next' => false,
            'has_prev' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Get transaction summary for filtered results
 *
 * @param array $filters - Array of filter parameters
 * @return array - Summary statistics
 */
function getTransactionSummary($filters = []) {
    try {
        $pdo = getDBConnection();

        // Check if payment_completed_at exists, fallback to created_at
        $check_column = $pdo->query("SHOW COLUMNS FROM orders LIKE 'payment_completed_at'");
        $date_column = $check_column->rowCount() > 0 ? 'payment_completed_at' : 'created_at';

        // Build WHERE clause (same as getTransactionLog)
        $where_conditions = ["o.payment_status = 'completed'", "o.status = 'paid'"];
        $params = [];

        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            $where_conditions[] = "DATE(o.$date_column) BETWEEN ? AND ?";
            $params[] = $filters['start_date'];
            $params[] = $filters['end_date'];
        }

        if (!empty($filters['billboard_type'])) {
            $where_conditions[] = "o.billboard_type = ?";
            $params[] = $filters['billboard_type'];
        }

        if (!empty($filters['customer_search'])) {
            $where_conditions[] = "(o.customer_name LIKE ? OR o.customer_email LIKE ? OR o.order_number LIKE ?)";
            $search_term = '%' . $filters['customer_search'] . '%';
            $params[] = $search_term;
            $params[] = $search_term;
            $params[] = $search_term;
        }

        if (!empty($filters['min_amount'])) {
            $where_conditions[] = "o.total_amount >= ?";
            $params[] = (float)$filters['min_amount'];
        }

        if (!empty($filters['max_amount'])) {
            $where_conditions[] = "o.total_amount <= ?";
            $params[] = (float)$filters['max_amount'];
        }

        $where_clause = implode(' AND ', $where_conditions);

        $query = "
            SELECT
                COUNT(*) as total_transactions,
                COALESCE(SUM(o.total_amount), 0) as total_revenue,
                COALESCE(AVG(o.total_amount), 0) as average_amount,
                MIN(o.$date_column) as first_transaction,
                MAX(o.$date_column) as last_transaction
            FROM orders o
            WHERE $where_clause
        ";

        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        $result = $stmt->fetch();

        return [
            'total_transactions' => (int)$result['total_transactions'],
            'total_revenue' => (float)$result['total_revenue'],
            'average_amount' => (float)$result['average_amount'],
            'first_transaction' => $result['first_transaction'],
            'last_transaction' => $result['last_transaction']
        ];

    } catch (Exception $e) {
        error_log("Error getting transaction summary: " . $e->getMessage());
        return [
            'total_transactions' => 0,
            'total_revenue' => 0,
            'average_amount' => 0,
            'first_transaction' => null,
            'last_transaction' => null,
            'error' => $e->getMessage()
        ];
    }
}
?>
