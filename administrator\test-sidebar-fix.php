<?php
/**
 * Test page to verify sidebar flicker fix
 * This page can be used to test the FOUC prevention implementation
 */

session_start();
require_once 'includes/auth.php';

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Location: index.php');
    exit;
}

// Get current admin info
$current_admin = getCurrentAdmin();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sidebar Test - Borges Media Admin</title>
    <link rel="stylesheet" href="../assets/css/admin-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-instructions {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .test-instructions h3 {
            color: #1976d2;
            margin-top: 0;
        }
        
        .test-steps {
            list-style: decimal;
            padding-left: 20px;
        }
        
        .test-steps li {
            margin-bottom: 8px;
        }
        
        .status-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .navigation-links {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        
        .nav-test-btn {
            padding: 10px 20px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .nav-test-btn:hover {
            background: #2980b9;
        }
    </style>
</head>
<body<?php echo (isset($_COOKIE['sidebarCollapsed']) && $_COOKIE['sidebarCollapsed'] === 'true') ? ' class="sidebar-collapsed"' : ''; ?>>
    <div class="admin-layout">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-content">
                <div class="header-brand">
                    <img src="../assets/images/bm-header-logo.png" alt="Borges Media Logo" class="admin-logo">
                    <h1>Borges Media Billboard Admin</h1>
                </div>
                <div class="header-user">
                    Welcome, <?php echo htmlspecialchars($current_admin['username']); ?>
                </div>
            </div>
        </header>
        
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Main Content -->
        <main class="admin-main">
            <div class="main-content">
                <h2>Sidebar Flicker Fix Test Page</h2>
                
                <div class="test-instructions">
                    <h3><i class="fas fa-bug"></i> Testing the Sidebar Flicker Fix</h3>
                    <p>This page helps verify that the sidebar flicker/jumping bug has been resolved.</p>
                    
                    <h4>Test Steps:</h4>
                    <ol class="test-steps">
                        <li>Toggle the sidebar to collapsed state using the toggle button</li>
                        <li>Navigate between admin pages using the links below</li>
                        <li>Observe that the sidebar maintains its collapsed state without any visual jumping or flickering</li>
                        <li>Try refreshing the page - the sidebar should remain in its saved state</li>
                        <li>Test with both expanded and collapsed states</li>
                    </ol>
                </div>
                
                <div class="test-content">
                    <h3>Current Sidebar State</h3>
                    <p>Server-side detection: 
                        <span class="status-indicator <?php echo (isset($_COOKIE['sidebarCollapsed']) && $_COOKIE['sidebarCollapsed'] === 'true') ? 'status-success' : 'status-warning'; ?>">
                            <?php echo (isset($_COOKIE['sidebarCollapsed']) && $_COOKIE['sidebarCollapsed'] === 'true') ? 'Collapsed' : 'Expanded'; ?>
                        </span>
                    </p>
                    <p>Client-side detection: <span id="clientSideState" class="status-indicator status-warning">Loading...</span></p>
                </div>
                
                <div class="test-content">
                    <h3>Navigation Test Links</h3>
                    <p>Use these links to test navigation between pages while observing sidebar behavior:</p>
                    
                    <div class="navigation-links">
                        <a href="orders.php" class="nav-test-btn">
                            <i class="fas fa-list-alt"></i> Orders
                        </a>
                        <a href="financial-reports.php" class="nav-test-btn">
                            <i class="fas fa-chart-line"></i> Financial Reports
                        </a>
                        <a href="history.php" class="nav-test-btn">
                            <i class="fas fa-history"></i> History
                        </a>
                        <a href="test-sidebar-fix.php" class="nav-test-btn">
                            <i class="fas fa-refresh"></i> Refresh Test Page
                        </a>
                    </div>
                </div>
                
                <div class="test-content">
                    <h3>Expected Behavior</h3>
                    <ul>
                        <li><strong>✅ No Flicker:</strong> Sidebar should not briefly expand before collapsing during page loads</li>
                        <li><strong>✅ State Persistence:</strong> Sidebar state should be maintained across page navigation</li>
                        <li><strong>✅ Smooth Transitions:</strong> Toggle animations should be smooth and consistent</li>
                        <li><strong>✅ Server-Client Sync:</strong> Server-side and client-side state detection should match</li>
                    </ul>
                </div>
            </div>
        </main>
    </div>
    
    <script>
        // Display client-side state detection
        document.addEventListener('DOMContentLoaded', function() {
            const clientStateElement = document.getElementById('clientSideState');
            const savedState = localStorage.getItem('sidebarCollapsed');
            const isCollapsed = savedState === 'true';
            
            clientStateElement.textContent = isCollapsed ? 'Collapsed' : 'Expanded';
            clientStateElement.className = 'status-indicator ' + (isCollapsed ? 'status-success' : 'status-warning');
            
            // Log state information for debugging
            console.log('=== Sidebar State Debug ===');
            console.log('localStorage:', savedState);
            console.log('Cookie:', document.cookie.includes('sidebarCollapsed=true'));
            console.log('Body class:', document.body.classList.contains('sidebar-collapsed'));
            console.log('Sidebar class:', document.getElementById('adminSidebar')?.classList.contains('collapsed'));
        });
    </script>
</body>
</html>
