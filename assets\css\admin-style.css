/* Admin Panel Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

/* Login Page Styles */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
}

.login-box {
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-logo {
    margin-bottom: 20px;
}

.login-logo img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    border-radius: 12px;
    background: #f8f9fa;
    padding: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.login-header h1 {
    color: #333;
    margin-bottom: 10px;
    font-size: 28px;
}

.login-header p {
    color: #666;
    font-size: 14px;
}

.login-form {
    margin-bottom: 20px;
}

.login-footer {
    text-align: center;
    margin-top: 20px;
}

.register-link {
    text-align: center;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.register-link p {
    color: #666;
    font-size: 14px;
}

.register-link a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}

.register-link a:hover {
    text-decoration: underline;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s;
    margin-right: 10px;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* Alerts */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 5px;
    font-weight: 500;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Admin Layout with CSS Custom Properties */
:root {
    --sidebar-width: 250px;
    --sidebar-collapsed-width: 60px;
}

/* FOUC Prevention - Apply collapsed state immediately during page load */
html.sidebar-collapsed-loading .admin-layout {
    grid-template-columns: var(--sidebar-collapsed-width) 1fr;
}

html.sidebar-collapsed-loading .admin-sidebar {
    width: var(--sidebar-collapsed-width);
}

html.sidebar-collapsed-loading .nav-text,
html.sidebar-collapsed-loading .user-name {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

html.sidebar-collapsed-loading .nav-link {
    justify-content: center;
}

html.sidebar-collapsed-loading .sidebar-footer {
    padding: 15px 14px;
}

html.sidebar-collapsed-loading .sidebar-toggle i {
    transform: rotate(0deg);
}

.admin-layout {
    display: grid;
    grid-template-areas:
        "header header"
        "sidebar main";
    grid-template-columns: var(--sidebar-width) 1fr;
    grid-template-rows: 60px 1fr;
    min-height: 100vh;
    transition: grid-template-columns 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Collapsed Sidebar Layout */
body.sidebar-collapsed .admin-layout,
.admin-sidebar.collapsed ~ .admin-main {
    grid-template-columns: var(--sidebar-collapsed-width) 1fr;
}

body.sidebar-collapsed .admin-layout {
    --sidebar-width: var(--sidebar-collapsed-width);
}

.admin-header {
    grid-area: header;
    background: #2c3e50;
    color: white;
    display: flex;
    align-items: center;
    padding: 0 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.header-brand {
    display: flex;
    align-items: center;
    gap: 15px;
}

.admin-logo {
    width: 40px;
    height: 40px;
    object-fit: contain;
    border-radius: 6px;
    background: rgba(255,255,255,0.1);
    padding: 3px;
}

.header-content h1 {
    font-size: 20px;
    font-weight: 600;
}

.header-user {
    font-size: 14px;
    color: #bdc3c7;
}

.admin-sidebar {
    grid-area: sidebar;
    background: #34495e;
    padding: 0;
    position: sticky;
    top: 60px;
    height: calc(100vh - 60px);
    overflow-y: auto;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    will-change: width;
}

/* Sidebar Toggle Button */
.sidebar-toggle {
    background: #2c3e50;
    border: none;
    color: #bdc3c7;
    padding: 15px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    border-bottom: 1px solid #2c3e50;
    position: relative;
    z-index: 1001;
    pointer-events: auto;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-toggle:hover {
    background: #1a252f;
    color: white;
}

.sidebar-toggle:focus {
    outline: 2px solid #3498db;
    outline-offset: -2px;
}

/* Ensure toggle button works in all states */
.sidebar-toggle {
    /* Override any potential interference */
    pointer-events: auto !important;
    z-index: 1001 !important;
    position: relative !important;
    cursor: pointer !important;
}

body.sidebar-collapsed .sidebar-toggle,
.admin-sidebar.collapsed .sidebar-toggle {
    background: #2c3e50;
    pointer-events: auto !important;
    z-index: 1001 !important;
}

body.sidebar-collapsed .sidebar-toggle:hover,
.admin-sidebar.collapsed .sidebar-toggle:hover {
    background: #1a252f;
    color: white;
}



/* Sidebar Navigation */
.sidebar-nav {
    flex: 1;
    padding: 10px 0;
}

.nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin-bottom: 2px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    gap: 12px;
}

.nav-link i {
    width: 20px;
    text-align: center;
    flex-shrink: 0;
    font-size: 16px;
}

.nav-text {
    white-space: nowrap;
    opacity: 1;
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1), width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-link:hover,
.nav-link.active {
    background: #2c3e50;
    color: white;
    border-right: 3px solid #3498db;
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #3498db;
}

/* Logout Button Styles */
.logout-btn {
    color: #e74c3c !important;
    margin-top: auto;
}

.logout-btn:hover {
    background: #c0392b !important;
    color: white !important;
    border-right: 3px solid #e74c3c !important;
}

/* Collapsed Sidebar Navigation */
body.sidebar-collapsed .nav-link,
.admin-sidebar.collapsed .nav-link {
    padding: 12px 20px;
    justify-content: center;
}

body.sidebar-collapsed .nav-text,
.admin-sidebar.collapsed .nav-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1), width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body.sidebar-collapsed .nav-link:hover .nav-text,
.admin-sidebar.collapsed .nav-link:hover .nav-text {
    opacity: 1;
    width: auto;
    position: absolute;
    left: var(--sidebar-collapsed-width);
    background: #2c3e50;
    padding: 8px 12px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    z-index: 1000;
    white-space: nowrap;
}

.admin-main {
    grid-area: main;
    padding: 30px;
    overflow-y: auto;
    transition: margin-left 0.3s ease;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 15px 20px;
    border-top: 1px solid #2c3e50;
    margin-top: auto;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #bdc3c7;
    font-size: 14px;
}

.user-info i {
    font-size: 18px;
    flex-shrink: 0;
}

.user-name {
    white-space: nowrap;
    opacity: 1;
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1), width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body.sidebar-collapsed .sidebar-footer,
.admin-sidebar.collapsed .sidebar-footer {
    padding: 15px 14px;
}

body.sidebar-collapsed .user-name,
.admin-sidebar.collapsed .user-name {
    opacity: 0;
    width: 0;
    overflow: hidden;
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1), width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Sidebar Overlay for Mobile */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 999;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-layout {
        grid-template-areas:
            "header"
            "main";
        grid-template-columns: 1fr;
        grid-template-rows: 60px 1fr;
    }
    
    .admin-sidebar {
        position: fixed;
        top: 60px;
        left: -250px;
        width: 250px;
        height: calc(100vh - 60px);
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    body.sidebar-collapsed .admin-sidebar,
    .admin-sidebar.collapsed {
        left: -250px;
    }
    
    body:not(.sidebar-collapsed) .admin-sidebar,
    .admin-sidebar:not(.collapsed) {
        left: 0;
    }
    
    body:not(.sidebar-collapsed) .sidebar-overlay {
        display: block;
    }
    
    .admin-main {
        padding: 20px 15px;
    }
}

@media (max-width: 480px) {
    .admin-main {
        padding: 15px 10px;
    }
    
    .sidebar-header {
        padding: 12px 15px;
    }
    
    .nav-link {
        padding: 10px 15px;
    }
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-card h3 {
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-number {
    font-size: 36px;
    font-weight: bold;
    color: #2c3e50;
}

/* Table Styles */
.table-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 1px;
}

.data-table tr:hover {
    background: #f8f9fa;
}

.text-center {
    text-align: center;
}

/* Status and Badge Styles */
.status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-in_progress {
    background: #cce5ff;
    color: #004085;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.badge-templated {
    background: #e7f3ff;
    color: #0066cc;
}

.badge-custom {
    background: #f0e6ff;
    color: #6600cc;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 30px;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    position: relative;
}

.close {
    position: absolute;
    right: 15px;
    top: 15px;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #999;
}

.close:hover {
    color: #333;
}

/* Filters */
.filters {
    background: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.filter-form {
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.filter-group label {
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.filter-group input[type="text"] {
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s;
    min-width: 500px;
    width: 100%;
    max-width: 400px;
}

.filter-group input[type="text"]:focus {
    outline: none;
    border-color: #667eea;
}

.filter-group input[type="text"]::placeholder {
    color: #999;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-layout {
        grid-template-areas:
            "header"
            "sidebar"
            "main";
        grid-template-columns: 1fr;
        grid-template-rows: 60px auto 1fr;
    }

    .admin-sidebar {
        padding: 10px 0;
        position: static;
        height: auto;
    }

    .header-brand {
        gap: 10px;
    }

    .admin-logo {
        width: 32px;
        height: 32px;
    }

    .header-content h1 {
        font-size: 18px;
    }

    .login-logo img {
        width: 60px;
        height: 60px;
    }
    
    .sidebar-nav {
        display: flex;
        overflow-x: auto;
    }
    
    .sidebar-nav ul {
        display: flex;
        white-space: nowrap;
    }
    
    .sidebar-nav li {
        margin-right: 5px;
        margin-bottom: 0;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .filter-form {
        flex-direction: column;
        align-items: stretch;
    }
    
    .data-table {
        font-size: 12px;
    }
    
    .data-table th,
    .data-table td {
        padding: 8px;
    }

    .pagination-container {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* Calendar Bookings Styles */
.booking-dates {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.booking-count {
    font-weight: 600;
    color: #667eea;
    font-size: 12px;
}

.booking-dates-list {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;
}

.booking-date {
    background: #e8f2ff;
    color: #1e40af;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
}

.booking-more {
    background: #f3f4f6;
    color: #6b7280;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-style: italic;
}

.booking-separator {
    color: #6b7280;
    font-size: 11px;
    font-weight: 500;
    margin: 0 2px;
}

.no-bookings {
    color: #9ca3af;
    font-style: italic;
    font-size: 12px;
}

/* Modal Booking Styles */
.modal-booking-info {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.modal-booking-info h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 16px;
}

.modal-booking-summary {
    margin-bottom: 15px;
}

.booking-count-large {
    background: #667eea;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 14px;
}

.modal-booking-dates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 8px;
}

.modal-booking-dates-flex {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    flex-wrap: wrap;
}

.modal-booking-separator {
    color: #6b7280;
    font-weight: 500;
    font-size: 14px;
}

.modal-booking-date {
    background: #e8f2ff;
    color: #1e40af;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    text-align: center;
    border: 1px solid #bfdbfe;
}

.no-bookings-modal {
    color: #6b7280;
    font-style: italic;
    text-align: center;
    padding: 20px;
    background: #f9fafb;
    border-radius: 6px;
    border: 2px dashed #d1d5db;
}

/* Enhanced Statistics Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

/* Enhanced Badge Styles */
.badge-success {
    background: #d4edda;
    color: #155724;
}

.badge-warning {
    background: #fff3cd;
    color: #856404;
}

.badge-info {
    background: #d1ecf1;
    color: #0c5460;
}

/* Enhanced Status Styles */
.status-paid {
    background: #d4edda;
    color: #155724;
}

.status-launched {
    background: #e2e3e5;
    color: #383d41;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #1e7e34;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #117a8b;
}

/* Image Modal Styles */
.modal-large {
    max-width: 90%;
    width: 800px;
}

/* Compact Modal Styles */
.modal-compact {
    max-width: 85%;
    width: 600px;
    max-height: 85vh;
}

.modal-header-compact {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.modal-header-compact .close {
    position: relative;
    right: 0;
    top: 0;
    margin-left: 15px;
    font-size: 20px;
}

.modal-title-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.modal-title-section h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.booking-chip {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 10px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid #bbdefb;
}

.booking-chip i {
    font-size: 11px;
}

#imageContainer {
    text-align: center;
    margin: 15px 0;
    max-height: 60vh;
    overflow: auto;
}

#billboardImage {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.image-actions {
    text-align: center;
    margin-top: 15px;
}

/* Responsive adjustments for compact modal */
@media (max-width: 768px) {
    .modal-compact {
        width: 95%;
        max-width: 95%;
    }

    .modal-title-section h3 {
        font-size: 16px;
    }

    .booking-chip {
        font-size: 11px;
        padding: 3px 8px;
    }

    #imageContainer {
        max-height: 50vh;
    }
}

/* Text Utilities */
.text-muted {
    color: #6c757d;
}

/* Table Responsive */
.table-responsive {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
}

.table th {
    background: #f8f9fa;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #dee2e6;
}

/* Pagination Styles */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.pagination-info {
    color: #666;
    font-size: 14px;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 5px;
}

.pagination-btn {
    display: inline-flex;
    align-items: center;
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    color: #333;
    text-decoration: none;
    border-radius: 5px;
    font-size: 14px;
    transition: all 0.3s;
    min-width: 40px;
    justify-content: center;
}

.pagination-btn:hover {
    background: #f8f9fa;
    border-color: #667eea;
    color: #667eea;
}

.pagination-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.pagination-dots {
    padding: 8px 4px;
    color: #999;
}
