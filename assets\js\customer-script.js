// Customer Landing Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeModal();
    initializeSmoothScrolling();
    initializeFormValidation();
    initializeAnimations();
    initializeCalendarIntegration();
});

// Modal functionality
function initializeModal() {
    const modal = document.getElementById('selectionModal');
    const closeBtn = document.querySelector('.close');
    
    if (closeBtn) {
        closeBtn.addEventListener('click', closeModal);
    }
    
    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            closeModal();
        }
    });
    
    // Close modal on Escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && modal.style.display === 'block') {
            closeModal();
        }
    });
}

// Select billboard type and open modal or redirect
function selectBillboardType(type) {
    if (type === 'custom') {
        // Redirect directly to custom billboard designer
        window.location.href = 'fabric-custom-billboard/index.php';
        return;
    }

    const modal = document.getElementById('selectionModal');
    const modalTitle = document.getElementById('modalTitle');
    const modalMessage = document.getElementById('modalMessage');
    const billboardTypeInput = document.getElementById('billboard_type');

    if (modal && modalTitle && modalMessage && billboardTypeInput) {
        billboardTypeInput.value = type;

        if (type === 'templated') {
            modalTitle.textContent = 'Templated Billboard Request';
            modalMessage.textContent = 'You have selected to create a templated billboard. Please provide your information to get started with our pre-designed templates.';
        }

        modal.style.display = 'block';

        // Focus on first input
        const firstInput = modal.querySelector('input[type="text"]');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
        }
    }
}

// Close modal
function closeModal() {
    const modal = document.getElementById('selectionModal');
    if (modal) {
        modal.style.display = 'none';
        
        // Reset form
        const form = document.getElementById('orderForm');
        if (form) {
            form.reset();
            clearValidationErrors();
        }
    }
}

// Smooth scrolling for navigation links
function initializeSmoothScrolling() {
    const navLinks = document.querySelectorAll('.nav a[href^="#"]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Form validation
function initializeFormValidation() {
    const form = document.getElementById('orderForm');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
            }
        });
        
        // Real-time validation
        const inputs = form.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            input.addEventListener('input', function() {
                clearFieldError(this);
            });
        });
    }
}

// Validate entire form
function validateForm() {
    const form = document.getElementById('orderForm');
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    clearValidationErrors();
    
    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });
    
    // Validate email format
    const emailField = document.getElementById('customer_email');
    if (emailField && emailField.value) {
        if (!isValidEmail(emailField.value)) {
            showFieldError(emailField, 'Please enter a valid email address.');
            isValid = false;
        }
    }
    
    // Validate phone format (if provided)
    const phoneField = document.getElementById('customer_phone');
    if (phoneField && phoneField.value) {
        if (!isValidPhone(phoneField.value)) {
            showFieldError(phoneField, 'Please enter a valid phone number.');
            isValid = false;
        }
    }
    
    return isValid;
}

// Validate individual field
function validateField(field) {
    if (field.hasAttribute('required') && !field.value.trim()) {
        showFieldError(field, 'This field is required.');
        return false;
    }
    
    if (field.type === 'email' && field.value && !isValidEmail(field.value)) {
        showFieldError(field, 'Please enter a valid email address.');
        return false;
    }
    
    clearFieldError(field);
    return true;
}

// Show field error
function showFieldError(field, message) {
    field.style.borderColor = '#dc3545';
    
    // Remove existing error message
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
    
    // Add error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.style.color = '#dc3545';
    errorDiv.style.fontSize = '12px';
    errorDiv.style.marginTop = '5px';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

// Clear field error
function clearFieldError(field) {
    field.style.borderColor = '#ddd';
    
    const errorDiv = field.parentNode.querySelector('.field-error');
    if (errorDiv) {
        errorDiv.remove();
    }
}

// Clear all validation errors
function clearValidationErrors() {
    const form = document.getElementById('orderForm');
    if (form) {
        const errorDivs = form.querySelectorAll('.field-error');
        errorDivs.forEach(div => div.remove());
        
        const inputs = form.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            input.style.borderColor = '#ddd';
        });
    }
}

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Phone validation
function isValidPhone(phone) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
    return phoneRegex.test(cleanPhone) && cleanPhone.length >= 10;
}

// Initialize animations
function initializeAnimations() {
    // Animate elements on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.service-item, .about-item, .button-card');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Add hover effects to button cards
document.addEventListener('DOMContentLoaded', function() {
    const buttonCards = document.querySelectorAll('.button-card');
    
    buttonCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});

// Loading state for form submission
function showLoadingState() {
    const submitBtn = document.querySelector('#orderForm button[type="submit"]');
    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.textContent = 'Submitting...';
        submitBtn.style.opacity = '0.7';
    }
}

// Reset loading state
function resetLoadingState() {
    const submitBtn = document.querySelector('#orderForm button[type="submit"]');
    if (submitBtn) {
        submitBtn.disabled = false;
        submitBtn.textContent = 'Submit Request';
        submitBtn.style.opacity = '1';
    }
}

// Handle form submission with loading state
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('orderForm');
    if (form) {
        form.addEventListener('submit', function() {
            if (validateForm()) {
                showLoadingState();
            }
        });
    }
});

// Character counter for textarea
function initializeCharacterCounter() {
    const textarea = document.getElementById('project_description');
    if (textarea) {
        const maxLength = 500;
        
        // Create counter element
        const counter = document.createElement('div');
        counter.className = 'character-counter';
        counter.style.fontSize = '12px';
        counter.style.color = '#666';
        counter.style.textAlign = 'right';
        counter.style.marginTop = '5px';
        
        textarea.parentNode.appendChild(counter);
        
        // Update counter
        function updateCounter() {
            const remaining = maxLength - textarea.value.length;
            counter.textContent = `${remaining} characters remaining`;
            
            if (remaining < 50) {
                counter.style.color = '#dc3545';
            } else {
                counter.style.color = '#666';
            }
        }
        
        textarea.addEventListener('input', updateCounter);
        textarea.setAttribute('maxlength', maxLength);
        updateCounter();
    }
}

// Initialize character counter when modal opens
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('selectionModal');
    if (modal) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    if (modal.style.display === 'block') {
                        setTimeout(initializeCharacterCounter, 100);
                    }
                }
            });
        });
        
        observer.observe(modal, { attributes: true });
    }
});

// ========================================
// CALENDAR INTEGRATION FUNCTIONS
// ========================================

// Initialize calendar integration
function initializeCalendarIntegration() {
    // Listen for calendar selection changes
    document.addEventListener('calendarSelectionChange', function(event) {
        const { selectedDates, isValid } = event.detail;
        updateUIBasedOnCalendarSelection(selectedDates, isValid);
    });

    // Initially disable action buttons
    updateUIBasedOnCalendarSelection([], false);
}

// Update UI based on calendar selection
function updateUIBasedOnCalendarSelection(selectedDates, isValid) {
    const proceedBtn = document.getElementById('proceedWithDatesBtn');
    const actionButtons = document.getElementById('actionButtons');
    const templatesBtn = document.getElementById('templatesBtn');
    const customBtn = document.getElementById('customBtn');
    const dateRequirementNotice = document.getElementById('dateRequirementNotice');
    const billboardOptionsSection = document.getElementById('billboardOptionsSection');

    if (proceedBtn) {
        proceedBtn.disabled = !isValid;
        proceedBtn.textContent = isValid ?
            `Continue with ${selectedDates.length} Selected Date${selectedDates.length > 1 ? 's' : ''}` :
            'Continue with Selected Dates';
    }

    // Store selected dates using order data manager
    if (selectedDates.length > 0 && window.orderDataManager) {
        window.orderDataManager.setSelectedDates(selectedDates);
    }

    // Keep legacy localStorage for backward compatibility
    if (selectedDates.length > 0) {
        localStorage.setItem('selectedBillboardDates', JSON.stringify(selectedDates));
        localStorage.setItem('bookingStartDate', selectedDates[0]);
        localStorage.setItem('bookingEndDate', selectedDates[selectedDates.length - 1]);
    } else {
        localStorage.removeItem('selectedBillboardDates');
        localStorage.removeItem('bookingStartDate');
        localStorage.removeItem('bookingEndDate');
    }

    // Show/hide billboard options section based on whether user has proceeded
    const hasProceeded = localStorage.getItem('calendarSelectionConfirmed') === 'true';

    if (hasProceeded && isValid) {
        if (billboardOptionsSection) {
            billboardOptionsSection.style.display = 'block';
        }
        if (dateRequirementNotice) {
            dateRequirementNotice.style.display = 'none';
        }
        if (actionButtons) {
            actionButtons.classList.remove('disabled');
        }
        if (templatesBtn) templatesBtn.disabled = false;
        if (customBtn) customBtn.disabled = false;
    } else {
        if (dateRequirementNotice) {
            dateRequirementNotice.style.display = 'block';
        }
        if (actionButtons) {
            actionButtons.classList.add('disabled');
        }
        if (templatesBtn) templatesBtn.disabled = true;
        if (customBtn) customBtn.disabled = true;
    }
}

// Proceed with selected dates
function proceedWithDates() {
    if (!billboardCalendar || !billboardCalendar.isSelectionValid()) {
        alert('Please select at least one date for your billboard display.');
        return;
    }

    const selectedDates = billboardCalendar.getSelectedDates();

    // Store confirmation in localStorage
    localStorage.setItem('calendarSelectionConfirmed', 'true');

    // Show billboard options section
    const billboardOptionsSection = document.getElementById('billboardOptionsSection');
    if (billboardOptionsSection) {
        billboardOptionsSection.style.display = 'block';
        billboardOptionsSection.scrollIntoView({ behavior: 'smooth' });
    }

    // Update UI
    updateUIBasedOnCalendarSelection(selectedDates, true);

    // Show success message
    showNotification(`Great! You've selected ${selectedDates.length} date${selectedDates.length > 1 ? 's' : ''} for your billboard. Now choose your design option below.`, 'success');
}

// Clear calendar selection
function clearCalendarSelection() {
    if (billboardCalendar) {
        billboardCalendar.clearSelection();
    }

    // Clear localStorage
    localStorage.removeItem('selectedBillboardDates');
    localStorage.removeItem('bookingStartDate');
    localStorage.removeItem('bookingEndDate');
    localStorage.removeItem('calendarSelectionConfirmed');

    // Hide billboard options section
    const billboardOptionsSection = document.getElementById('billboardOptionsSection');
    if (billboardOptionsSection) {
        billboardOptionsSection.style.display = 'none';
    }

    // Update UI
    updateUIBasedOnCalendarSelection([], false);

    showNotification('Calendar selection cleared.', 'info');
}

// Proceed to templates with date validation
function proceedToTemplates() {
    if (!validateDateSelection()) {
        return;
    }

    // Store billboard type for templated option
    if (window.orderDataManager) {
        window.orderDataManager.setBillboardType('templated');
    }
    // Legacy localStorage support
    localStorage.setItem('billboardType', 'templated');

    window.location.href = 'fabric-templated-billboard/index.php';
}

// Updated selectBillboardType function with date validation
function selectBillboardType(type) {
    if (!validateDateSelection()) {
        return;
    }

    if (type === 'custom') {
        // Store billboard type for custom option
        if (window.orderDataManager) {
            window.orderDataManager.setBillboardType('custom');
        }
        // Legacy localStorage support
        localStorage.setItem('billboardType', 'custom');

        // Redirect to custom billboard designer
        window.location.href = 'fabric-custom-billboard/index.php';
        return;
    }

    // Store billboard type in order data manager
    if (window.orderDataManager) {
        window.orderDataManager.setBillboardType(type);
    }
    // Legacy localStorage support
    localStorage.setItem('billboardType', type);

    const modal = document.getElementById('selectionModal');
    const modalTitle = document.getElementById('modalTitle');
    const modalMessage = document.getElementById('modalMessage');
    const billboardTypeInput = document.getElementById('billboard_type');

    if (modal && modalTitle && modalMessage && billboardTypeInput) {
        billboardTypeInput.value = type;

        if (type === 'templated') {
            modalTitle.textContent = 'Templated Billboard Request';
            modalMessage.textContent = 'You have selected to create a templated billboard. Please provide your contact information to get started with our professional templates.';
        } else {
            modalTitle.textContent = 'Custom Billboard Request';
            modalMessage.textContent = 'You have selected to create a custom billboard. Please provide your contact information and project details to get started.';
        }

        modal.style.display = 'block';

        // Focus on first input
        setTimeout(() => {
            const firstInput = modal.querySelector('input[type="text"]');
            if (firstInput) {
                firstInput.focus();
            }
        }, 100);
    }
}

// Validate date selection
function validateDateSelection() {
    const selectedDates = localStorage.getItem('selectedBillboardDates');
    const confirmed = localStorage.getItem('calendarSelectionConfirmed');

    if (!selectedDates || !confirmed || confirmed !== 'true') {
        alert('Please select your billboard display dates first by using the calendar above.');

        // Scroll to calendar
        const calendarSection = document.querySelector('.calendar-section');
        if (calendarSection) {
            calendarSection.scrollIntoView({ behavior: 'smooth' });
        }

        return false;
    }

    return true;
}

// Show notification helper
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;

    // Add styles if not already added
    if (!document.getElementById('notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                max-width: 400px;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                animation: slideInRight 0.3s ease;
            }
            .notification-success { background: #d4edda; color: #155724; border-left: 4px solid #28a745; }
            .notification-info { background: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }
            .notification-warning { background: #fff3cd; color: #856404; border-left: 4px solid #ffc107; }
            .notification-error { background: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
            .notification-content { display: flex; justify-content: space-between; align-items: center; }
            .notification-close { background: none; border: none; font-size: 18px; cursor: pointer; opacity: 0.7; }
            .notification-close:hover { opacity: 1; }
            @keyframes slideInRight { from { transform: translateX(100%); } to { transform: translateX(0); } }
        `;
        document.head.appendChild(styles);
    }

    // Add to page
    document.body.appendChild(notification);

    // Add close functionality
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        notification.remove();
    });

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
