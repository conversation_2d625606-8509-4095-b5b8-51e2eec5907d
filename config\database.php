<?php
// Database configuration for InfinityFree
// IMPORTANT: Update these values with your InfinityFree database details
define('DB_HOST', 'sql123.epizy.com'); // Replace with your actual host
define('DB_USERNAME', 'epiz_12345678'); // Replace with your actual username
define('DB_PASSWORD', 'your_password'); // Replace with your actual password
define('DB_NAME', 'epiz_12345678_billboard'); // Replace with your actual database name

// Create database connection
function getDBConnection() {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
            DB_USERNAME,
            DB_PASSWORD,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ]
        );

        // Set MySQL session variables to handle large packets
        // Note: max_allowed_packet is read-only at session level, must be set globally
        try {
            $pdo->exec("SET SESSION wait_timeout = 300"); // 5 minutes
            $pdo->exec("SET SESSION interactive_timeout = 300"); // 5 minutes
        } catch (Exception $e) {
            // Ignore if we can't set these - they're not critical
            error_log("Could not set MySQL session variables: " . $e->getMessage());
        }

        return $pdo;
    } catch (PDOException $e) {
        die("Database connection failed: " . $e->getMessage());
    }
}

// Test database connection
function testDBConnection() {
    try {
        $pdo = getDBConnection();
        return true;
    } catch (Exception $e) {
        return false;
    }
}
?>
