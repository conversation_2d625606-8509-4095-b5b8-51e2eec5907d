/* ========================================
   MOBILE-FIRST TOOLBAR STYLES
   ======================================== */

/* Mobile Toolbar Container */
.mobile-toolbar {
    background: var(--white);
    border-top: 1px solid var(--gray-200);
    box-shadow: 0 -4px 6px -1px rgb(0 0 0 / 0.1);
    position: relative;
    z-index: var(--z-sticky);
}

/* Toolbar Header */
.toolbar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.toolbar-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.toolbar-toggle {
    width: var(--touch-target-comfortable);
    height: var(--touch-target-comfortable);
    border: none;
    border-radius: var(--radius-md);
    background: var(--white);
    color: var(--gray-600);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-sm);
}

.toolbar-toggle:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.toolbar-toggle:active {
    transform: scale(0.95);
}

.toolbar-toggle i {
    transition: transform 0.3s ease;
}

.toolbar-toggle.collapsed i {
    transform: rotate(180deg);
}

/* Toolbar Content */
.toolbar-content {
    max-height: 70vh;
    overflow-y: auto;
    transition: max-height 0.3s ease;
    -webkit-overflow-scrolling: touch;
}

.toolbar-content.collapsed {
    max-height: 0;
    overflow: hidden;
}

/* Tool Sections */
.tool-section {
    border-bottom: 1px solid var(--gray-200);
}

.tool-section:last-child {
    border-bottom: none;
}

/* Section Header */
.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4);
    background: var(--gray-50);
    cursor: pointer;
    user-select: none;
}

.section-header:hover {
    background: var(--gray-100);
}

.section-title {
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.section-toggle {
    width: var(--space-8);
    height: var(--space-8);
    border: none;
    background: none;
    color: var(--gray-500);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: var(--radius-sm);
}

.section-toggle:hover {
    background: var(--gray-200);
    color: var(--gray-700);
}

.section-toggle i {
    transition: transform 0.3s ease;
}

.section-toggle.collapsed i {
    transform: rotate(-90deg);
}

/* Section Content */
.section-content {
    padding: var(--space-4);
    transition: max-height 0.3s ease;
    overflow: hidden;
}

.section-content.collapsed {
    max-height: 0 !important;
    padding-top: 0;
    padding-bottom: 0;
}

/* Tool Grid */
.tool-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--space-3);
    margin-bottom: var(--space-4);
}

.tool-grid:last-child {
    margin-bottom: 0;
}

/* Tool Buttons */
.tool-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-4);
    min-height: var(--touch-target-comfortable);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    background: var(--white);
    color: var(--gray-700);
    font-size: var(--text-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    text-decoration: none;
    /* Mobile touch optimizations */
    touch-action: manipulation;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.tool-btn i {
    font-size: var(--text-lg);
}

.tool-btn span {
    font-size: var(--text-xs);
    line-height: 1.2;
}

/* Tool Button Variants */
.tool-btn.primary {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.tool-btn.primary:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.tool-btn.secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border-color: var(--gray-300);
}

.tool-btn.secondary:hover {
    background: var(--gray-200);
    color: var(--gray-900);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.tool-btn.success {
    background: var(--success-color);
    color: var(--white);
    border-color: var(--success-color);
}

.tool-btn.success:hover {
    background: #047857;
    border-color: #047857;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.tool-btn.danger {
    background: var(--danger-color);
    color: var(--white);
    border-color: var(--danger-color);
}

.tool-btn.danger:hover {
    background: #b91c1c;
    border-color: #b91c1c;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.tool-btn:active {
    transform: scale(0.95);
}

.tool-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Property Groups */
.property-group {
    margin-bottom: var(--space-4);
}

.property-group:last-child {
    margin-bottom: 0;
}

.property-label {
    display: block;
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--space-2);
}

.property-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-2);
}

/* Form Controls */
.property-select,
.property-input {
    width: 100%;
    padding: var(--space-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    background: var(--white);
    color: var(--gray-900);
    font-size: var(--text-sm);
    transition: all 0.2s ease;
}

.property-select:focus,
.property-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

/* Input with Controls */
.input-with-controls {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.input-with-controls .property-input {
    flex: 1;
}

.input-controls {
    display: flex;
    gap: var(--space-1);
}

.input-btn {
    width: var(--space-8);
    height: var(--space-8);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-sm);
    background: var(--white);
    color: var(--gray-600);
    font-size: var(--text-sm);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.input-btn:hover {
    background: var(--gray-50);
    color: var(--gray-900);
}

.input-btn:active {
    transform: scale(0.95);
}

/* Toggle Groups */
.toggle-group {
    display: flex;
    gap: var(--space-2);
    flex-wrap: wrap;
}

.toggle-btn {
    width: var(--touch-target-comfortable);
    height: var(--touch-target-comfortable);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    background: var(--white);
    color: var(--gray-600);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: var(--text-sm);
    font-weight: 600;
}

.toggle-btn:hover {
    background: var(--gray-50);
    color: var(--gray-900);
}

.toggle-btn.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.toggle-btn:active {
    transform: scale(0.95);
}

/* Shadow Toggle Special Styling */
.shadow-toggle {
    width: var(--space-8);
    height: var(--space-8);
    font-size: var(--text-xs);
}

/* Color Picker */
.color-picker-wrapper {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.color-picker {
    width: var(--touch-target-comfortable);
    height: var(--touch-target-comfortable);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    background: none;
    cursor: pointer;
    padding: 0;
}

.color-picker::-webkit-color-swatch-wrapper {
    padding: 0;
}

.color-picker::-webkit-color-swatch {
    border: none;
    border-radius: calc(var(--radius-md) - 1px);
}

.color-preview {
    width: var(--space-6);
    height: var(--space-6);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-sm);
    background: #000000;
}

/* Range Sliders */
.range-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: var(--gray-200);
    outline: none;
    -webkit-appearance: none;
    margin: var(--space-2) 0;
}

.range-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: 2px solid var(--white);
    box-shadow: var(--shadow-sm);
}

.range-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: 2px solid var(--white);
    box-shadow: var(--shadow-sm);
}

.range-value {
    font-size: var(--text-xs);
    color: var(--gray-600);
    font-weight: 500;
    min-width: 3rem;
    text-align: right;
}

/* Sub Properties */
.sub-property {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--space-3);
    margin-bottom: var(--space-3);
}

.sub-property:last-child {
    margin-bottom: 0;
}

.sub-label {
    font-size: var(--text-xs);
    color: var(--gray-600);
    font-weight: 500;
    min-width: 4rem;
}

.sub-property .range-slider {
    flex: 1;
    margin: 0;
}

/* Shadow Controls */
.shadow-controls {
    padding: var(--space-3);
    background: var(--gray-50);
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-200);
    margin-top: var(--space-2);
}

/* Text Properties Panel */
.text-properties {
    margin-top: var(--space-4);
    padding: var(--space-4);
    background: var(--gray-50);
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-200);
}

/* Enhanced Font Dropdown Styling */
.property-select#fontFamily {
    font-size: var(--text-base);
    line-height: 1.4;
    padding: var(--space-3) var(--space-4);
    min-height: 44px;
}

.property-select#fontFamily option {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-base);
    line-height: 1.4;
    min-height: 32px;
}

/* Ensure font previews are visible and properly styled */
.property-select#fontFamily option[style*="font-family"] {
    font-display: swap;
}

/* Font loading indicator for dropdown */
.property-select#fontFamily.loading {
    background-image: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12a9 9 0 11-6.219-8.56"/></svg>');
    background-repeat: no-repeat;
    background-position: right var(--space-3) center;
    background-size: 16px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive Adjustments */

/* Small tablets and large phones (576px and up) */
@media (min-width: 36rem) {
    .tool-grid {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    }
    
    .toolbar-content {
        max-height: 60vh;
    }
}

/* Medium tablets (768px and up) - Hide mobile toolbar on desktop */
@media (min-width: 48rem) {
    .mobile-toolbar {
        display: none; /* Hidden on desktop - replaced by sidebars */
    }
}

/* Large tablets and small desktops (992px and up) */
@media (min-width: 62rem) {
    .mobile-toolbar {
        display: none; /* Ensure hidden on large screens too */
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    .tool-btn {
        /* Disable hover effects on touch devices */
        transition: transform 0.1s ease;
    }

    .tool-btn:hover {
        transform: none;
        background: var(--white);
        border-color: var(--gray-300);
        box-shadow: none;
    }

    .tool-btn.primary:hover {
        background: var(--primary-color);
        border-color: var(--primary-color);
        box-shadow: none;
    }

    .tool-btn.secondary:hover {
        background: var(--gray-100);
        color: var(--gray-700);
        border-color: var(--gray-300);
        box-shadow: none;
    }

    .tool-btn.success:hover {
        background: var(--success-color);
        border-color: var(--success-color);
        box-shadow: none;
    }

    .tool-btn.danger:hover {
        background: var(--danger-color);
        border-color: var(--danger-color);
        box-shadow: none;
    }

    .tool-btn:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }

    .section-header:hover {
        background: var(--gray-50);
    }

    /* Prevent double-tap zoom and improve touch responsiveness */
    .tool-btn,
    .section-header,
    .toolbar-toggle {
        touch-action: manipulation;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    }
}

/* Focus styles for keyboard navigation */
.tool-btn:focus-visible,
.property-select:focus-visible,
.property-input:focus-visible,
.toggle-btn:focus-visible,
.color-picker:focus-visible,
.range-slider:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.toolbar-toggle:focus-visible,
.section-toggle:focus-visible,
.input-btn:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}