/* ========================================
   MOBILE TOOLBAR MANAGER
   ======================================== */

/**
 * MobileToolbarManager - Manages mobile-first toolbar interactions
 * Handles collapsible sections, touch-friendly controls, and responsive behavior
 */
class MobileToolbarManager {
    constructor(canvasManager) {
        this.canvasManager = canvasManager;
        this.canvas = canvasManager.canvas;
        
        // DOM elements
        this.toolbar = document.getElementById('mobileToolbar');
        this.toolbarToggle = document.getElementById('toolbarToggle');
        this.toolbarContent = document.getElementById('toolbarContent');
        
        // State management
        this.isCollapsed = false;
        this.activeSections = new Set(['backgroundSection']); // Default open sections
        this.isMobile = this.detectMobile();
        
        // <PERSON><PERSON> states
        this.currentTool = null;
        this.toolStates = new Map();
        
        this.init();
    }
    
    /**
     * Initialize toolbar manager
     */
    init() {
        this.setupEventListeners();
        this.setupSectionToggles();
        this.setupToolButtons();
        this.setupResponsiveBehavior();
        this.initializeDefaultState();
        
        console.log('MobileToolbarManager initialized');
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Toolbar toggle
        if (this.toolbarToggle) {
            this.toolbarToggle.addEventListener('click', () => {
                this.toggleToolbar();
            });
        }
        
        // Canvas events
        this.canvasManager.on('selection:changed', (e) => {
            this.handleSelectionChange(e.detail.objects);
        });
        
        this.canvasManager.on('object:changed', (e) => {
            this.updateToolStates();
        });
        
        // Window resize
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));
        
        // Orientation change for mobile
        if (this.isMobile) {
            window.addEventListener('orientationchange', () => {
                setTimeout(() => this.handleResize(), 100);
            });
        }

        // Export quality and format change listeners
        const exportQuality = document.getElementById('exportQuality');
        if (exportQuality) {
            exportQuality.addEventListener('change', (e) => {
                console.log('Export quality changed to:', e.target.value);
                this.showToast(`Export quality set to ${e.target.value}`);
            });
        }

        const exportFormat = document.getElementById('exportFormat');
        if (exportFormat) {
            exportFormat.addEventListener('change', (e) => {
                console.log('Export format changed to:', e.target.value);
                this.showToast(`Export format set to ${e.target.value.toUpperCase()}`);
            });
        }
    }
    
    /**
     * Setup section toggle functionality
     */
    setupSectionToggles() {
        const sectionHeaders = this.toolbar.querySelectorAll('.section-header');
        
        sectionHeaders.forEach(header => {
            header.addEventListener('click', (e) => {
                const targetId = header.querySelector('.section-toggle').dataset.target;
                this.toggleSection(targetId);
            });
        });
    }
    
    /**
     * Setup tool button functionality
     */
    setupToolButtons() {
        // Background tools
        this.setupBackgroundTools();

        // Text tools
        this.setupTextTools();

        // Export tools
        this.setupExportTools();

        // Canvas controls
        this.setupCanvasControls();

        // Add mobile touch optimizations
        this.setupMobileTouchOptimizations();
    }
    
    /**
     * Setup background tool buttons
     */
    setupBackgroundTools() {
        const chooseBackgroundBtn = document.getElementById('chooseBackground');
        const clearBackgroundBtn = document.getElementById('clearBackground');
        const addImageBtn = document.getElementById('addImage');
        const addStickerBtn = document.getElementById('addSticker');
        const clearAllBtn = document.getElementById('clearAll');
        
        if (chooseBackgroundBtn) {
            chooseBackgroundBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.openBackgroundModal();
            });
        }

        if (clearBackgroundBtn) {
            clearBackgroundBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.canvasManager.clearBackground();
                this.showToast('Background cleared');
            });
        }

        if (addImageBtn) {
            addImageBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.triggerImageUpload();
            });
        }

        if (addStickerBtn) {
            addStickerBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.openStickerModal();
            });
        }

        if (clearAllBtn) {
            clearAllBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                if (confirm('Are you sure you want to clear all elements?')) {
                    this.canvasManager.clearCanvas();
                    this.showToast('Canvas cleared');
                }
            });
        }
    }
    
    /**
     * Setup text tool buttons and controls
     */
    setupTextTools() {
        const addTextBtn = document.getElementById('addText');
        const textProperties = document.getElementById('textProperties');
        
        if (addTextBtn) {
            addTextBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const textObj = this.canvasManager.addText('Your Text Here');
                this.showSection('textTools');
                this.showTextProperties();
                this.showToast('Text added - tap to edit');
            });
        }
        
        // Font family
        const fontFamily = document.getElementById('fontFamily');
        if (fontFamily) {
            fontFamily.addEventListener('change', (e) => {
                this.updateSelectedTextProperty('fontFamily', e.target.value);
            });
        }
        
        // Font size
        const fontSize = document.getElementById('fontSize');
        const fontSizeUp = document.getElementById('fontSizeUp');
        const fontSizeDown = document.getElementById('fontSizeDown');
        
        if (fontSize) {
            fontSize.addEventListener('input', (e) => {
                this.updateSelectedTextProperty('fontSize', parseInt(e.target.value));
            });
        }
        
        if (fontSizeUp) {
            fontSizeUp.addEventListener('click', () => {
                this.adjustFontSize(2);
            });
        }
        
        if (fontSizeDown) {
            fontSizeDown.addEventListener('click', () => {
                this.adjustFontSize(-2);
            });
        }
        
        // Text style toggles
        const boldToggle = document.getElementById('boldToggle');
        const italicToggle = document.getElementById('italicToggle');
        
        if (boldToggle) {
            boldToggle.addEventListener('click', () => {
                this.toggleTextStyle('fontWeight', 'bold', 'normal');
                boldToggle.classList.toggle('active');
            });
        }
        
        if (italicToggle) {
            italicToggle.addEventListener('click', () => {
                this.toggleTextStyle('fontStyle', 'italic', 'normal');
                italicToggle.classList.toggle('active');
            });
        }
        
        // Text alignment
        const alignButtons = document.querySelectorAll('[data-align]');
        alignButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const alignment = e.currentTarget.dataset.align;
                this.updateSelectedTextProperty('textAlign', alignment);
                this.updateAlignmentButtons(alignment);
            });
        });
        
        // Font color
        const fontColor = document.getElementById('fontColor');
        if (fontColor) {
            fontColor.addEventListener('change', (e) => {
                this.updateSelectedTextProperty('fill', e.target.value);
                this.updateColorPreview('fontColorPreview', e.target.value);
            });
        }
        
        // Text shadow
        this.setupTextShadowControls();
    }
    
    /**
     * Setup text shadow controls
     */
    setupTextShadowControls() {
        const shadowToggle = document.getElementById('shadowToggle');
        const shadowControls = document.getElementById('shadowControls');
        
        if (shadowToggle) {
            shadowToggle.addEventListener('click', () => {
                const isActive = shadowToggle.classList.toggle('active');
                if (shadowControls) {
                    shadowControls.style.display = isActive ? 'block' : 'none';
                }
                this.toggleTextShadow(isActive);
            });
        }
        
        // Shadow properties
        const shadowColor = document.getElementById('shadowColor');
        const shadowBlur = document.getElementById('shadowBlur');
        const shadowOffsetX = document.getElementById('shadowOffsetX');
        const shadowOffsetY = document.getElementById('shadowOffsetY');
        const shadowOpacity = document.getElementById('shadowOpacity');
        const shadowType = document.getElementById('shadowType');
        const offsetControls = document.getElementById('offsetControls');

        if (shadowColor) {
            shadowColor.addEventListener('change', (e) => {
                this.updateTextShadow();
                this.updateColorPreview('shadowColorPreview', e.target.value);
            });
        }

        // Shadow type change handler
        if (shadowType) {
            shadowType.addEventListener('change', () => {
                const isDropShadow = shadowType.value === 'drop';
                if (offsetControls) {
                    offsetControls.style.display = isDropShadow ? 'block' : 'none';
                }

                // Apply better defaults based on type
                if (shadowType.value === 'glow') {
                    // Glow: higher blur, no offset
                    if (shadowBlur) shadowBlur.value = 8;
                    if (shadowOffsetX) shadowOffsetX.value = 0;
                    if (shadowOffsetY) shadowOffsetY.value = 0;
                } else {
                    // Drop shadow: moderate blur, visible offset
                    if (shadowBlur) shadowBlur.value = 3;
                    if (shadowOffsetX) shadowOffsetX.value = 2;
                    if (shadowOffsetY) shadowOffsetY.value = 2;
                }

                // Update range value displays
                [shadowBlur, shadowOffsetX, shadowOffsetY].forEach(control => {
                    if (control) this.updateRangeValue(control);
                });

                this.updateTextShadow();
            });
        }

        [shadowBlur, shadowOffsetX, shadowOffsetY, shadowOpacity].forEach(control => {
            if (control) {
                control.addEventListener('input', () => {
                    this.updateTextShadow();
                    this.updateRangeValue(control);
                });
            }
        });
    }
    
    /**
     * Setup export tool buttons
     */
    setupExportTools() {
        const downloadBtn = document.getElementById('downloadImage');
        const checkoutBtn = document.getElementById('checkoutBtn');
        
        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => {
                this.exportImage();
            });
        }
        
        if (checkoutBtn) {
            checkoutBtn.addEventListener('click', () => {
                this.emit('checkout:requested');
            });
        }
    }
    
    /**
     * Setup canvas control buttons
     */
    setupCanvasControls() {
        const zoomFit = document.getElementById('zoomFit');
        const zoomReset = document.getElementById('zoomReset');

        if (zoomFit) {
            zoomFit.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.canvasManager.core.fitToViewport();
                this.showToast('Fit to screen');
            });
        }

        if (zoomReset) {
            zoomReset.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.canvasManager.core.resetZoom();
                this.showToast('Zoom reset');
            });
        }
    }

    /**
     * Setup mobile touch optimizations
     */
    setupMobileTouchOptimizations() {
        if (!this.isMobile) return;

        // Get all tool buttons
        const toolButtons = this.toolbar.querySelectorAll('.tool-btn');

        toolButtons.forEach(button => {
            // Add touch start event for immediate feedback
            button.addEventListener('touchstart', (e) => {
                e.stopPropagation();
                button.style.transform = 'scale(0.95)';
                button.style.transition = 'transform 0.1s ease';
            }, { passive: true });

            // Reset transform on touch end
            button.addEventListener('touchend', (e) => {
                e.stopPropagation();
                setTimeout(() => {
                    button.style.transform = '';
                    button.style.transition = '';
                }, 100);
            }, { passive: true });

            // Prevent context menu on long press
            button.addEventListener('contextmenu', (e) => {
                e.preventDefault();
            });
        });

        // Optimize section headers for mobile
        const sectionHeaders = this.toolbar.querySelectorAll('.section-header');
        sectionHeaders.forEach(header => {
            header.addEventListener('touchstart', (e) => {
                e.stopPropagation();
            }, { passive: true });
        });
    }
    
    /**
     * Setup responsive behavior
     */
    setupResponsiveBehavior() {
        // Auto-collapse toolbar on mobile in landscape
        if (this.isMobile) {
            this.handleResize();
        }
    }
    
    /**
     * Initialize default state
     */
    initializeDefaultState() {
        // Show default sections
        this.activeSections.forEach(sectionId => {
            this.showSection(sectionId.replace('Section', 'Tools'));
        });
        
        // Hide text properties initially
        this.hideTextProperties();
    }
    
    /**
     * Toggle toolbar visibility
     */
    toggleToolbar() {
        this.isCollapsed = !this.isCollapsed;
        
        if (this.toolbarContent) {
            this.toolbarContent.classList.toggle('collapsed', this.isCollapsed);
        }
        
        if (this.toolbarToggle) {
            this.toolbarToggle.classList.toggle('collapsed', this.isCollapsed);
        }
        
        this.emit('toolbar:toggled', { collapsed: this.isCollapsed });
    }
    
    /**
     * Toggle section visibility
     */
    toggleSection(sectionId) {
        const sectionContent = document.getElementById(sectionId);
        const sectionToggle = document.querySelector(`[data-target="${sectionId}"]`);
        
        if (!sectionContent || !sectionToggle) return;
        
        const isCollapsed = sectionContent.classList.toggle('collapsed');
        sectionToggle.classList.toggle('collapsed', isCollapsed);
        
        if (isCollapsed) {
            this.activeSections.delete(sectionId);
        } else {
            this.activeSections.add(sectionId);
        }
        
        this.emit('section:toggled', { sectionId, collapsed: isCollapsed });
    }
    
    /**
     * Show section
     */
    showSection(sectionId) {
        const sectionContent = document.getElementById(sectionId);
        const sectionToggle = document.querySelector(`[data-target="${sectionId}"]`);
        
        if (sectionContent) {
            sectionContent.classList.remove('collapsed');
        }
        if (sectionToggle) {
            sectionToggle.classList.remove('collapsed');
        }
        
        this.activeSections.add(sectionId);
    }
    
    /**
     * Hide section
     */
    hideSection(sectionId) {
        const sectionContent = document.getElementById(sectionId);
        const sectionToggle = document.querySelector(`[data-target="${sectionId}"]`);
        
        if (sectionContent) {
            sectionContent.classList.add('collapsed');
        }
        if (sectionToggle) {
            sectionToggle.classList.add('collapsed');
        }
        
        this.activeSections.delete(sectionId);
    }
    
    /**
     * Handle selection change
     */
    handleSelectionChange(objects) {
        const hasTextSelection = objects.some(obj => obj.type === 'text' || obj.type === 'i-text');

        if (hasTextSelection) {
            this.showTextProperties();
            this.updateTextControls(objects.find(obj => obj.type === 'text' || obj.type === 'i-text'));
        } else {
            this.hideTextProperties();
        }

        this.emit('selection:handled', { objects, hasText: hasTextSelection });
    }
    
    /**
     * Show text properties panel
     */
    showTextProperties() {
        const textProperties = document.getElementById('textProperties');
        if (textProperties) {
            textProperties.style.display = 'block';
        }
    }
    
    /**
     * Hide text properties panel
     */
    hideTextProperties() {
        const textProperties = document.getElementById('textProperties');
        if (textProperties) {
            textProperties.style.display = 'none';
        }
    }
    
    /**
     * Update text controls based on selected text
     */
    updateTextControls(textObj) {
        if (!textObj) return;
        
        // Update font family
        const fontFamily = document.getElementById('fontFamily');
        if (fontFamily) {
            fontFamily.value = textObj.fontFamily || 'Inter';
        }
        
        // Update font size
        const fontSize = document.getElementById('fontSize');
        if (fontSize) {
            fontSize.value = textObj.fontSize || 24;
        }
        
        // Update style toggles
        const boldToggle = document.getElementById('boldToggle');
        const italicToggle = document.getElementById('italicToggle');
        
        if (boldToggle) {
            boldToggle.classList.toggle('active', textObj.fontWeight === 'bold');
        }
        
        if (italicToggle) {
            italicToggle.classList.toggle('active', textObj.fontStyle === 'italic');
        }
        
        // Update alignment
        this.updateAlignmentButtons(textObj.textAlign || 'left');
        
        // Update font color
        const fontColor = document.getElementById('fontColor');
        if (fontColor) {
            fontColor.value = textObj.fill || '#000000';
            this.updateColorPreview('fontColorPreview', textObj.fill || '#000000');
        }
        
        // Update shadow controls
        this.updateShadowControls(textObj);
    }
    
    /**
     * Update selected text property
     */
    updateSelectedTextProperty(property, value) {
        const activeObjects = this.canvas.getActiveObjects();
        const textObjects = activeObjects.filter(obj => obj.type === 'text' || obj.type === 'i-text');

        textObjects.forEach(obj => {
            obj.set(property, value);
        });

        this.canvas.renderAll();
    }
    
    /**
     * Toggle text style
     */
    toggleTextStyle(property, activeValue, inactiveValue) {
        const activeObjects = this.canvas.getActiveObjects();
        const textObjects = activeObjects.filter(obj => obj.type === 'text' || obj.type === 'i-text');

        textObjects.forEach(obj => {
            const currentValue = obj.get(property);
            const newValue = currentValue === activeValue ? inactiveValue : activeValue;
            obj.set(property, newValue);
        });

        this.canvas.renderAll();
    }
    
    /**
     * Adjust font size
     */
    adjustFontSize(delta) {
        const activeObjects = this.canvas.getActiveObjects();
        const textObjects = activeObjects.filter(obj => obj.type === 'text' || obj.type === 'i-text');

        textObjects.forEach(obj => {
            const currentSize = obj.fontSize || 24;
            const newSize = Math.max(8, Math.min(200, currentSize + delta));
            obj.set('fontSize', newSize);
        });

        // Update input
        const fontSize = document.getElementById('fontSize');
        if (fontSize && textObjects.length > 0) {
            fontSize.value = textObjects[0].fontSize;
        }

        this.canvas.renderAll();
    }
    
    /**
     * Update alignment buttons
     */
    updateAlignmentButtons(alignment) {
        const alignButtons = document.querySelectorAll('[data-align]');
        alignButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.align === alignment);
        });
    }
    
    /**
     * Update color preview
     */
    updateColorPreview(previewId, color) {
        const preview = document.getElementById(previewId);
        if (preview) {
            preview.style.backgroundColor = color;
        }
    }
    
    /**
     * Update range value display
     */
    updateRangeValue(rangeInput) {
        const valueDisplay = document.getElementById(rangeInput.id + 'Value');
        if (valueDisplay) {
            let value = rangeInput.value;
            if (rangeInput.id === 'shadowOpacity') {
                value += '%';
            }
            valueDisplay.textContent = value;
        }
    }
    
    /**
     * Toggle text shadow
     */
    toggleTextShadow(enabled) {
        const activeObjects = this.canvas.getActiveObjects();
        const textObjects = activeObjects.filter(obj => obj.type === 'text' || obj.type === 'i-text');

        textObjects.forEach(obj => {
            if (enabled) {
                this.updateTextShadow();
            } else {
                obj.set('shadow', null);
            }
        });

        this.canvas.renderAll();
    }
    
    /**
     * Update text shadow
     */
    updateTextShadow() {
        const shadowColor = document.getElementById('shadowColor')?.value || '#000000';
        const shadowBlur = document.getElementById('shadowBlur')?.value || 0;
        const shadowOffsetX = document.getElementById('shadowOffsetX')?.value || 0;
        const shadowOffsetY = document.getElementById('shadowOffsetY')?.value || 0;
        const shadowOpacity = document.getElementById('shadowOpacity')?.value || 100;
        const shadowType = document.getElementById('shadowType')?.value || 'glow';

        // Determine offset based on shadow type
        let offsetX, offsetY;
        if (shadowType === 'glow') {
            // Glow effect: no offset, visible through blur
            offsetX = 0;
            offsetY = 0;
        } else {
            // Drop shadow: use offset values
            offsetX = parseInt(shadowOffsetX);
            offsetY = parseInt(shadowOffsetY);
        }

        const shadow = new fabric.Shadow({
            color: shadowColor,
            blur: parseInt(shadowBlur),
            offsetX: offsetX,
            offsetY: offsetY,
            opacity: parseInt(shadowOpacity) / 100
        });

        const activeObjects = this.canvas.getActiveObjects();
        const textObjects = activeObjects.filter(obj => obj.type === 'text' || obj.type === 'i-text');

        textObjects.forEach(obj => {
            obj.set('shadow', shadow);
        });

        this.canvas.renderAll();
    }
    
    /**
     * Update shadow controls
     */
    updateShadowControls(textObj) {
        const shadow = textObj.shadow;
        const shadowToggle = document.getElementById('shadowToggle');
        const shadowControls = document.getElementById('shadowControls');
        const shadowType = document.getElementById('shadowType');
        const offsetControls = document.getElementById('offsetControls');

        if (shadow) {
            if (shadowToggle) shadowToggle.classList.add('active');
            if (shadowControls) shadowControls.style.display = 'block';

            // Update shadow values
            const shadowColor = document.getElementById('shadowColor');
            const shadowBlur = document.getElementById('shadowBlur');
            const shadowOffsetX = document.getElementById('shadowOffsetX');
            const shadowOffsetY = document.getElementById('shadowOffsetY');
            const shadowOpacity = document.getElementById('shadowOpacity');

            if (shadowColor) {
                shadowColor.value = shadow.color || '#000000';
                this.updateColorPreview('shadowColorPreview', shadow.color || '#000000');
            }
            if (shadowBlur) {
                shadowBlur.value = shadow.blur || 0;
                this.updateRangeValue(shadowBlur);
            }
            if (shadowOffsetX) {
                shadowOffsetX.value = shadow.offsetX || 0;
                this.updateRangeValue(shadowOffsetX);
            }
            if (shadowOffsetY) {
                shadowOffsetY.value = shadow.offsetY || 0;
                this.updateRangeValue(shadowOffsetY);
            }
            if (shadowOpacity) {
                shadowOpacity.value = (shadow.opacity || 1) * 100;
                this.updateRangeValue(shadowOpacity);
            }

            // Update shadow type and offset controls visibility
            if (shadowType && offsetControls) {
                const hasOffset = (shadow.offsetX !== 0 || shadow.offsetY !== 0);
                if (hasOffset) {
                    shadowType.value = 'drop';
                    offsetControls.style.display = 'block';
                } else {
                    shadowType.value = 'glow';
                    offsetControls.style.display = 'none';
                }
            }
        } else {
            if (shadowToggle) shadowToggle.classList.remove('active');
            if (shadowControls) shadowControls.style.display = 'none';
        }
    }
    
    /**
     * Trigger image upload
     */
    triggerImageUpload() {
        const fileInput = document.getElementById('imageUpload');
        if (fileInput) {
            fileInput.click();
            
            fileInput.onchange = (e) => {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (event) => {
                        this.canvasManager.addImage(event.target.result)
                            .then(() => {
                                this.showToast('Image added successfully');
                            })
                            .catch(() => {
                                this.showToast('Failed to add image', 'error');
                            });
                    };
                    reader.readAsDataURL(file);
                }
            };
        }
    }
    
    /**
     * Export image
     */
    exportImage() {
        const quality = document.getElementById('exportQuality')?.value || 'standard';
        const format = document.getElementById('exportFormat')?.value || 'png';

        // Map quality options to multipliers (1x, 2x, 3x, 4x, 6x)
        let multiplier = 1;
        switch (quality) {
            case 'web': multiplier = 1; break;
            case 'standard': multiplier = 2; break;
            case 'high': multiplier = 3; break;
            case 'ultra': multiplier = 4; break;
            case 'super': multiplier = 6; break;
            default: multiplier = 2; break;
        }

        console.log(`Exporting with quality: ${quality}, multiplier: ${multiplier}x, format: ${format}`);

        const dataURL = this.canvasManager.exportAsImage({
            format: format,
            quality: format === 'jpeg' ? 0.9 : 1,
            multiplier: multiplier
        });

        // Create descriptive filename with quality info
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const qualityLabel = quality.charAt(0).toUpperCase() + quality.slice(1);
        const filename = `billboard-${qualityLabel}-${multiplier}x-${timestamp}.${format}`;

        // Download image
        const link = document.createElement('a');
        link.download = filename;
        link.href = dataURL;
        link.click();

        // Calculate and show file size info
        const base64Length = dataURL.length - (dataURL.indexOf(',') + 1);
        const sizeInBytes = (base64Length * 3) / 4;
        const sizeInMB = (sizeInBytes / (1024 * 1024)).toFixed(2);

        this.showToast(`Image downloaded successfully (${sizeInMB}MB)`);
    }
    
    /**
     * Handle window resize
     */
    handleResize() {
        if (this.isMobile) {
            const isLandscape = window.innerWidth > window.innerHeight;
            if (isLandscape && !this.isCollapsed) {
                this.toggleToolbar();
            }
        }
    }
    
    /**
     * Update tool states
     */
    updateToolStates() {
        // Update any tool-specific states here
        this.emit('tool:states:updated');
    }

    /**
     * Open sticker selection modal
     */
    openStickerModal() {
        const modal = document.getElementById('stickerModal');
        if (modal) {
            modal.style.display = 'flex';
            modal.classList.add('show');

            // Initialize sticker manager if not already done
            if (!this.stickerManager) {
                this.stickerManager = new StickerManager(this.canvasManager);

                // Initialize sticker controls manager
                this.stickerControlsManager = new StickerControlsManager(this.canvasManager);

                // Add color picker to sticker properties panel
                const colorPickerContainer = document.getElementById('stickerColorPickerContainer');
                if (colorPickerContainer) {
                    colorPickerContainer.appendChild(this.stickerControlsManager.getColorPickerElement());
                }

                // Setup modal close handlers
                const closeBtn = document.getElementById('stickerModalClose');
                const cancelBtn = document.getElementById('stickerModalCancel');

                if (closeBtn) {
                    closeBtn.addEventListener('click', () => {
                        this.closeStickerModal();
                    });
                }

                if (cancelBtn) {
                    cancelBtn.addEventListener('click', () => {
                        this.closeStickerModal();
                    });
                }

                // Close modal when clicking overlay
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        this.closeStickerModal();
                    }
                });
            }
        }
    }

    /**
     * Close sticker selection modal
     */
    closeStickerModal() {
        const modal = document.getElementById('stickerModal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300); // Wait for transition to complete
        }
    }

    /**
     * Open background selection modal
     */
    openBackgroundModal() {
        // Initialize background manager if not already done
        if (!this.backgroundManager) {
            this.backgroundManager = new MobileBackgroundManager(this.canvasManager);
        }

        // Open the modal
        this.backgroundManager.openModal();
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'success') {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        
        // Style toast
        Object.assign(toast.style, {
            position: 'fixed',
            bottom: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            background: type === 'error' ? '#dc2626' : '#059669',
            color: 'white',
            padding: '12px 24px',
            borderRadius: '8px',
            zIndex: '9999',
            fontSize: '14px',
            fontWeight: '500',
            boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });
        
        document.body.appendChild(toast);
        
        // Show toast
        setTimeout(() => {
            toast.style.opacity = '1';
        }, 100);
        
        // Hide and remove toast
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }
    
    /**
     * Detect mobile device
     */
    detectMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               (navigator.maxTouchPoints && navigator.maxTouchPoints > 2);
    }
    
    /**
     * Debounce function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    /**
     * Event emitter
     */
    emit(eventName, data = {}) {
        const event = new CustomEvent(`toolbar:${eventName}`, {
            detail: { ...data, toolbar: this }
        });
        document.dispatchEvent(event);
    }
    
    /**
     * Event listener
     */
    on(eventName, handler) {
        document.addEventListener(`toolbar:${eventName}`, handler);
    }
    
    /**
     * Remove event listener
     */
    off(eventName, handler) {
        document.removeEventListener(`toolbar:${eventName}`, handler);
    }
}

// Export for global use
window.MobileToolbarManager = MobileToolbarManager;
