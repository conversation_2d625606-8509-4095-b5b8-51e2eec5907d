/* ========================================
   MODAL PANELS AND DIALOGS - CHECKOUT MODAL STRUCTURE
   ======================================== */

/* Text Customization Panel - Exactly like Checkout Modal */
.text-customization-panel, .image-upload-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: none;
    align-items: center;
    justify-content: center;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.text-customization-panel.show, .image-upload-panel.show {
    display: flex;
}

/* Panel Overlay - Like Checkout Modal */
.text-customization-panel::before, .image-upload-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(4px);
    z-index: -1;
}

/* Panel Content Container - Like checkout-modal-content */
.text-customization-panel > div, .image-upload-panel > div {
    position: relative;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 700px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Text Panel Header - Like Checkout Modal Header */
.text-panel-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.text-panel-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.text-panel-header i {
    margin-right: 10px;
}

.text-panel-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.text-panel-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Text Panel Body - Like Checkout Modal Body */
.text-panel-body {
    padding: 30px;
    overflow-y: auto;
    flex: 1;
    /* Enhanced scrolling behavior */
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    overscroll-behavior: contain;
}

.text-panel-body label {
    font-weight: 500;
    margin-bottom: 8px;
    color: #555;
    font-size: 0.9rem;
    display: block;
}

.text-panel-body input[type="text"],
.text-panel-body input[type="color"],
.text-panel-body input[type="range"],
.text-panel-body input[type="file"],
.text-panel-body select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.2s;
}

.text-panel-body input[type="color"] {
    height: 50px;
    padding: 4px;
}

.text-panel-body input:focus,
.text-panel-body select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Text Panel Footer - Exactly Like Checkout Modal Footer */
.text-panel-footer {
    background: #f8f9fa;
    padding: 20px 30px;
    border-top: 1px solid #e9ecef;
}

/* Text Panel Actions - Like Checkout Actions */
.text-panel-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Legacy styles for backward compatibility */
.apply-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 1rem;
    min-height: 44px;
}

.apply-btn:hover {
    background: #218838;
}

.apply-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* Text Panel Buttons - Exactly Like Checkout Modal Buttons */
.text-panel-actions .btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    text-decoration: none;
}

.text-panel-actions .btn i {
    margin-right: 8px;
}

.text-panel-actions .btn-secondary {
    background: #6c757d;
    color: white;
}

.text-panel-actions .btn-secondary:hover {
    background: #5a6268;
}

.text-panel-actions .btn-primary {
    background: #28a745;
    color: white;
}

.text-panel-actions .btn-primary:hover {
    background: #218838;
}

.text-panel-actions .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Form Grid for Text Panel */
.text-panel-body .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.text-panel-body .form-group {
    display: flex;
    flex-direction: column;
}

.text-panel-body .form-group.full-width {
    grid-column: 1 / -1;
}

.text-panel-body .form-group label {
    margin-bottom: 5px;
    color: #2c3e50;
    font-weight: 500;
}

/* Font Size Value Display */
#fontSizeValue {
    font-weight: 600;
    color: #007bff;
    margin-left: 4px;
}

/* Range Input Specific Styling for Text Panel */
.text-panel-body input[type="range"] {
    padding: 0 !important;
    height: 8px !important;
    margin: 12px 0 !important;
}

.remove-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    width: 100%;
}

.remove-btn:hover {
    background: #c82333;
}

/* Font Family Dropdown Enhancements */
.panel-content select#fontFamily {
    font-size: 1rem;
    line-height: 1.4;
}

.panel-content select#fontFamily option {
    padding: 8px;
    font-size: 1rem;
    line-height: 1.4;
}

/* Ensure font previews work in dropdown */
.panel-content select#fontFamily option[style*="font-family"] {
    font-display: swap;
}

/* Font loading indicator */
.font-loading-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
