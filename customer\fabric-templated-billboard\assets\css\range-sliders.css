/* ========================================
   RANGE SLIDER STYLES - CROSS BROWSER
   ======================================== */

/* Range Slider Styling - Cross Browser Compatible */
.panel-content input[type="range"],
.text-panel-body input[type="range"] {
    -webkit-appearance: none !important;
    appearance: none !important;
    width: 100% !important;
    height: 8px !important;
    background: #ddd !important;
    border-radius: 4px !important;
    outline: none !important;
    margin: 15px 0 !important;
    padding: 0 !important;
    border: none !important;
    box-shadow: none !important;
}

/* WebKit/Blink (Chrome, Safari, Edge) */
.panel-content input[type="range"]::-webkit-slider-track,
.text-panel-body input[type="range"]::-webkit-slider-track {
    width: 100%;
    height: 8px;
    cursor: pointer;
    background: #ddd;
    border-radius: 4px;
    border: none;
}

.panel-content input[type="range"]::-webkit-slider-thumb,
.text-panel-body input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    margin-top: -8px; /* Centers thumb on track */
}

/* Firefox */
.panel-content input[type="range"]::-moz-range-track,
.text-panel-body input[type="range"]::-moz-range-track {
    width: 100%;
    height: 8px;
    cursor: pointer;
    background: #ddd;
    border-radius: 4px;
    border: none;
}

.panel-content input[type="range"]::-moz-range-thumb,
.text-panel-body input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Internet Explorer/Edge */
.panel-content input[type="range"]::-ms-track,
.text-panel-body input[type="range"]::-ms-track {
    width: 100%;
    height: 8px;
    cursor: pointer;
    background: transparent;
    border-color: transparent;
    color: transparent;
}

.panel-content input[type="range"]::-ms-fill-lower,
.text-panel-body input[type="range"]::-ms-fill-lower {
    background: #ddd;
    border-radius: 4px;
}

.panel-content input[type="range"]::-ms-fill-upper,
.text-panel-body input[type="range"]::-ms-fill-upper {
    background: #ddd;
    border-radius: 4px;
}

.panel-content input[type="range"]::-ms-thumb,
.text-panel-body input[type="range"]::-ms-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Focus states */
.panel-content input[type="range"]:focus::-webkit-slider-thumb,
.text-panel-body input[type="range"]:focus::-webkit-slider-thumb {
    box-shadow: 0 0 0 3px rgba(0,123,255,0.3), 0 2px 4px rgba(0,0,0,0.2);
}

.panel-content input[type="range"]:focus::-moz-range-thumb,
.text-panel-body input[type="range"]:focus::-moz-range-thumb {
    box-shadow: 0 0 0 3px rgba(0,123,255,0.3), 0 2px 4px rgba(0,0,0,0.2);
}

/* Shadow Controls Range Sliders */
.range-slider {
    -webkit-appearance: none !important;
    appearance: none !important;
    width: 100% !important;
    height: 6px !important;
    background: #ddd !important;
    border-radius: 3px !important;
    outline: none !important;
    margin: 8px 0 !important;
    padding: 0 !important;
    border: none !important;
    box-shadow: none !important;
}

/* WebKit/Blink for shadow controls */
.range-slider::-webkit-slider-track {
    width: 100%;
    height: 6px;
    cursor: pointer;
    background: #ddd;
    border-radius: 3px;
    border: none;
}

.range-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    margin-top: -8px; /* Centers thumb on track */
}

/* Firefox for shadow controls */
.range-slider::-moz-range-track {
    width: 100%;
    height: 6px;
    cursor: pointer;
    background: #ddd;
    border-radius: 3px;
    border: none;
}

.range-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Internet Explorer/Edge for shadow controls */
.range-slider::-ms-track {
    width: 100%;
    height: 6px;
    cursor: pointer;
    background: transparent;
    border-color: transparent;
    color: transparent;
}

.range-slider::-ms-fill-lower {
    background: #ddd;
    border-radius: 3px;
}

.range-slider::-ms-fill-upper {
    background: #ddd;
    border-radius: 3px;
}

.range-slider::-ms-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.range-value {
    font-size: 0.8rem;
    color: #666;
    text-align: center;
    margin-top: 4px;
    min-width: 3rem;
    font-weight: 500;
}

/* Shadow Controls Specific Styles */
.shadow-controls {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    margin-top: 12px;
}

.sub-property {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    margin-bottom: 12px;
}

.sub-property:last-child {
    margin-bottom: 0;
}

.sub-label {
    font-size: 0.85rem;
    color: #666;
    font-weight: 500;
    min-width: 4rem;
    flex-shrink: 0;
}

.sub-property .range-slider {
    flex: 1;
    margin: 0;
}

.sub-property .range-value {
    margin-top: 0;
    margin-left: 8px;
    text-align: right;
}

/* Property Header for Shadow Toggle */
.property-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.property-label {
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

.toggle-btn {
    width: 32px;
    height: 32px;
    border: 2px solid #ddd;
    border-radius: 6px;
    background: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.8rem;
    color: #666;
    transition: all 0.2s ease;
}

.toggle-btn.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

.toggle-btn:hover {
    border-color: #007bff;
}

/* Ensure shadow controls are properly displayed when active */
.shadow-controls[style*="grid"] {
    display: grid !important;
    gap: 12px;
}

.shadow-controls[style*="block"] {
    display: block !important;
}

/* Offset Controls */
#offsetControls {
    margin-top: 8px;
}

#offsetControls[style*="block"] {
    display: block !important;
}

/* Form Group for Shadow */
.form-group.shadow-group {
    margin-bottom: 16px;
}

.form-group.shadow-group .property-header {
    margin-bottom: 12px;
}
