# Refresh Protection System

## Overview

The refresh protection system prevents accidental page reloads on critical pages like the Stripe checkout page, protecting users from losing their payment progress or interrupting payment transactions.

## Features

### 🛡️ Comprehensive Protection

- **F5 Key Protection** - Blocks F5 refresh attempts
- **Keyboard Shortcuts** - Blocks Ctrl+R, Cmd+R, Ctrl+F5, Ctrl+Shift+R
- **Browser Refresh Button** - Shows confirmation dialog for browser refresh button
- **Browser Navigation** - Protects against back/forward button navigation
- **Tab/Window Closing** - Shows warning when trying to close tab during payment
- **Context Menu Disabled** - Prevents right-click refresh in payment areas

### 💳 Payment-Specific Enhancements

- **Smart Payment Detection** - Automatically detects Stripe payment completion
- **Enhanced Messages** - Context-aware warning messages during payment
- **Visual Indicators** - Shows protection status indicator
- **Process Monitoring** - Tracks payment start/completion states
- **Automatic Disabling** - Disables protection after successful payment

## Implementation

### Files Involved

1. **`refresh-protection.js`** - Core protection utility
2. **`stripe-checkout.php`** - Payment page with enhanced protection
3. **`navigation-guard.js`** - Access validation (complementary)

### Auto-Initialization

The system automatically initializes on:
- `fabric-templated-billboard` pages (standard protection)
- `fabric-custom-billboard` pages (standard protection)  
- `stripe-checkout` pages (enhanced payment protection)

### Manual Usage

```javascript
// Enable standard protection
const protection = RefreshProtection.enable({
    message: 'Custom warning message'
});

// Enable enhanced payment protection
const paymentProtection = RefreshProtection.enablePaymentProtection({
    message: 'Payment-specific warning message'
});

// Temporarily allow navigation
RefreshProtection.allowNavigation(5000); // 5 seconds

// Allow payment completion navigation
RefreshProtection.allowPaymentCompletion(15000); // 15 seconds

// Disable protection
protection.disable();
```

## Protection Mechanisms

### 1. Keyboard Protection

Intercepts and blocks:
- `F5` - Standard refresh
- `Ctrl+R` / `Cmd+R` - Refresh shortcut
- `Ctrl+F5` - Hard refresh
- `Ctrl+Shift+R` - Alternative hard refresh
- `Ctrl+W` - Close tab (during payment)
- `Alt+F4` - Close window (during payment)

### 2. Browser Event Protection

- **beforeunload** - Shows confirmation dialog when leaving page
- **popstate** - Prevents back/forward navigation with confirmation
- **contextmenu** - Disables right-click menu in payment areas

### 3. Payment Process Monitoring

- Tracks payment form submission
- Updates protection messages during processing
- Monitors for Stripe redirect parameters
- Automatically allows navigation on completion

## Configuration Options

```javascript
const options = {
    message: 'Custom warning message',
    enableKeyboardProtection: true,    // Default: true
    enableBeforeUnloadProtection: true, // Default: true
    enablePopstateProtection: true     // Default: true
};
```

## Payment Page Integration

The Stripe checkout page includes:

1. **Enhanced Protection Initialization**
   ```javascript
   window.refreshProtectionInstance = RefreshProtection.enablePaymentProtection({
       message: 'Payment-specific warning'
   });
   ```

2. **Payment Completion Detection**
   - Monitors URL for `payment_intent` parameters
   - Detects `redirect_status` indicators
   - Automatically allows navigation on success

3. **Process State Tracking**
   - Tracks payment form submission
   - Updates protection during processing
   - Disables protection after completion

## Testing

Use the test page to verify protection:
```
http://localhost:8000/customer/shared/test-refresh-protection.html
```

### Test Scenarios

1. **Keyboard Shortcuts** - Try F5, Ctrl+R, etc.
2. **Browser Actions** - Use refresh button, back button
3. **Payment Simulation** - Test enhanced protection during payment
4. **Manual Controls** - Enable/disable protection manually

## Browser Compatibility

- ✅ Chrome/Chromium
- ✅ Firefox
- ✅ Safari
- ✅ Edge
- ✅ Mobile browsers

## Security Considerations

- Protection can be bypassed by determined users
- Serves as a safety net against accidental actions
- Does not replace server-side payment validation
- Works alongside Stripe's built-in protections

## Troubleshooting

### Protection Not Working

1. Check browser console for errors
2. Verify scripts are loaded correctly
3. Ensure no JavaScript conflicts
4. Test with browser developer tools

### False Positives

1. Use `allowNavigation()` for legitimate redirects
2. Check payment completion detection logic
3. Verify URL parameter monitoring

### "Changes you made may not be saved" Warning During Payment Redirect

**Problem:** Browser shows warning when redirecting to success page after payment.

**Solution:** The system now automatically disables protection during payment completion:

```javascript
// In stripe-checkout.js - before payment confirmation
RefreshProtection.disableForPaymentCompletion();

// This completely removes all beforeunload listeners
// and sets global flags to prevent warnings
```

**Key Methods:**
- `RefreshProtection.disableForPaymentCompletion()` - Complete protection removal
- `RefreshProtection.disableAll()` - Standard protection removal
- Global flags: `window.paymentCompleted`, `window.paymentInProgress`

**Testing:** Use `test-payment-redirect.html` to verify redirect behavior.

### Performance Impact

- Minimal overhead from event listeners
- No impact on payment processing speed
- Visual indicators auto-hide after 3 seconds
- Protection cleanly removed during payment completion

## API Reference

### RefreshProtection Class

#### Static Methods
- `RefreshProtection.enable(options)` - Enable standard protection
- `RefreshProtection.enablePaymentProtection(options)` - Enable payment protection
- `RefreshProtection.allowNavigation(duration)` - Allow navigation temporarily
- `RefreshProtection.allowPaymentCompletion(duration)` - Allow payment completion
- `RefreshProtection.disableAll()` - Disable all protection

#### Instance Methods
- `enable()` - Enable protection
- `disable()` - Disable protection
- `temporaryDisable(duration)` - Disable temporarily
- `allowNavigation(duration)` - Allow navigation
- `updateMessage(message)` - Update warning message
- `isProtectionEnabled()` - Check if enabled

## Changelog

### v2.0 (Current)
- Added enhanced payment protection
- Implemented visual indicators
- Added payment process monitoring
- Enhanced keyboard shortcut protection
- Added context menu disabling

### v1.0
- Basic refresh protection
- Keyboard shortcut blocking
- Browser event protection
- Auto-initialization
