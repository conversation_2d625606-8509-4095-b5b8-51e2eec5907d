/* ========================================
   UNIFIED CHECKOUT MODAL STYLES
   ======================================== */

.checkout-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.checkout-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(4px);
}

.checkout-modal-content {
    position: relative;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.checkout-modal-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.checkout-modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.checkout-modal-header i {
    margin-right: 10px;
}

.checkout-modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.checkout-modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.checkout-modal-body {
    padding: 30px;
    overflow-y: auto;
    flex: 1;
}

/* Order Summary Section */
.order-summary-section {
    margin-bottom: 30px;
}

.order-summary-section h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
}

.order-summary-section h3 i {
    margin-right: 10px;
    color: #28a745;
}

.summary-grid {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-item.total-row {
    border-top: 2px solid #28a745;
    margin-top: 10px;
    padding-top: 15px;
    font-weight: 600;
    font-size: 1.1rem;
}

.summary-label {
    color: #6c757d;
    font-weight: 500;
}

.summary-value {
    color: #2c3e50;
    font-weight: 600;
}

.total-amount {
    color: #28a745;
    font-size: 1.2rem;
}

/* Terms Section */
.terms-section {
    margin-bottom: 30px;
}

.terms-section h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
}

.terms-section h3 i {
    margin-right: 10px;
    color: #dc3545;
}

.terms-content {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 20px;
}

.terms-scroll-area {
    max-height: 300px;
    overflow-y: auto;
}

.terms-item {
    margin-bottom: 15px;
}

.terms-item.optional {
    border-top: 1px solid #ffeaa7;
    padding-top: 15px;
    margin-top: 20px;
}

/* Custom Checkbox Styles */
.checkbox-container {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    font-size: 0.95rem;
    line-height: 1.4;
}

.checkbox-container input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: white;
    border: 2px solid #ddd;
    border-radius: 4px;
    margin-right: 12px;
    margin-top: 2px;
    flex-shrink: 0;
    position: relative;
    transition: all 0.2s;
}

.checkbox-container:hover .checkmark {
    border-color: #28a745;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
    background: #28a745;
    border-color: #28a745;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-text {
    color: #2c3e50;
}

.checkbox-text a {
    color: #007bff;
    text-decoration: none;
}

.checkbox-text a:hover {
    text-decoration: underline;
}

/* Customer Information Section */
.customer-info-section {
    margin-bottom: 20px;
}

.customer-info-section h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
}

.customer-info-section h3 i {
    margin-right: 10px;
    color: #007bff;
}

.customer-form {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.form-group {
    flex: 1;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #2c3e50;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.2s;
}

.form-group input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Modal Footer */
.checkout-modal-footer {
    background: #f8f9fa;
    padding: 20px 30px;
    border-top: 1px solid #e9ecef;
}

.checkout-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    text-decoration: none;
}

.btn i {
    margin-right: 8px;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-primary {
    background: #28a745;
    color: white;
}

.btn-primary:hover {
    background: #218838;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Terms Modal Styles */
.terms-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10001;
    display: flex;
    align-items: center;
    justify-content: center;
}

.terms-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
}

.terms-modal-content {
    position: relative;
    background: white;
    border-radius: 12px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.terms-modal-header {
    background: #007bff;
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.terms-modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

.terms-modal-body {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
}

.terms-text h3 {
    color: #2c3e50;
    margin-top: 20px;
    margin-bottom: 10px;
}

.terms-text h4 {
    color: #495057;
    margin-top: 15px;
    margin-bottom: 8px;
}

.terms-text p {
    margin-bottom: 10px;
    line-height: 1.6;
}

.terms-text ul {
    margin-left: 20px;
    margin-bottom: 15px;
}

.terms-modal-footer {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .checkout-modal-content {
        width: 90%;
        max-width: 90vw;
        max-height: 80vh;
        margin: 30px;
    }

    .checkout-modal-header {
        padding: 16px 20px;
        flex-wrap: wrap;
        gap: 10px;
    }

    .checkout-modal-header h2 {
        font-size: 1.3rem;
        flex: 1;
        min-width: 0;
    }

    .checkout-modal-close {
        width: 32px;
        height: 32px;
        font-size: 1.3rem;
        padding: 6px;
    }

    .checkout-modal-body,
    .checkout-modal-footer {
        padding: 16px 20px;
    }

    .form-row {
        flex-direction: column;
        gap: 12px;
    }

    .checkout-actions {
        flex-direction: column;
        gap: 12px;
    }

    .btn {
        width: 100%;
        justify-content: center;
        padding: 14px 20px;
        font-size: 1rem;
    }

    .summary-grid {
        padding: 16px;
    }

    .terms-content {
        padding: 16px;
    }

    .customer-form {
        padding: 16px;
    }
}

/* Medium screens (tablets and small phones in landscape) */
@media (max-width: 640px) and (min-width: 481px) {
    .checkout-modal-content {
        width: 85%;
        max-width: 85vw;
        max-height: 78vh;
        margin: 25px;
    }
}

/* Extra small screens (phones in portrait) */
@media (max-width: 480px) {
    .checkout-modal-content {
        width: 88%;
        max-width: 88vw;
        max-height: 75vh;
        margin: 25px;
        border-radius: 12px;
    }

    .checkout-modal-header {
        padding: 12px 16px;
    }

    .checkout-modal-header h2 {
        font-size: 1.2rem;
    }

    .checkout-modal-close {
        width: 28px;
        height: 28px;
        font-size: 1.2rem;
        padding: 4px;
    }

    .checkout-modal-body,
    .checkout-modal-footer {
        padding: 12px 16px;
    }

    .summary-grid,
    .terms-content,
    .customer-form {
        padding: 12px;
    }

    .form-group input {
        padding: 12px;
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .btn {
        padding: 16px 20px;
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .summary-item {
        padding: 10px 0;
        font-size: 0.9rem;
    }

    .checkbox-container {
        font-size: 0.9rem;
    }

    .checkmark {
        width: 18px;
        height: 18px;
        margin-right: 10px;
    }
}
