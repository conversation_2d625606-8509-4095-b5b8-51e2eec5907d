<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Guard Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🔍 Navigation Guard Debug Tool</h1>
    
    <div class="debug-section">
        <h2>📋 Current Status</h2>
        <button class="button" onclick="checkCurrentStatus()">Check Status</button>
        <button class="button" onclick="simulateCheckoutData()">Simulate Checkout Data</button>
        <button class="button" onclick="clearAllData()">Clear All Data</button>
        <div id="currentStatus"></div>
    </div>

    <div class="debug-section">
        <h2>🧪 Test Payment Page Access</h2>
        <button class="button" onclick="testPaymentAccess()">Test Payment Page Access</button>
        <div id="paymentTestResult"></div>
    </div>

    <div class="debug-section">
        <h2>📊 LocalStorage Data</h2>
        <button class="button" onclick="showLocalStorageData()">Show LocalStorage Data</button>
        <div id="localStorageData"></div>
    </div>

    <div class="debug-section">
        <h2>🔧 Manual Data Setup</h2>
        <button class="button" onclick="setupValidSession()">Setup Valid Session</button>
        <button class="button" onclick="setupCheckoutData()">Setup Checkout Data</button>
        <div id="setupResult"></div>
    </div>

    <!-- Load the navigation guard script -->
    <script src="navigation-guard.js"></script>
    
    <script>
        function checkCurrentStatus() {
            const statusDiv = document.getElementById('currentStatus');
            
            if (typeof NavigationGuard === 'undefined') {
                statusDiv.innerHTML = '<div class="status error">❌ NavigationGuard class not available</div>';
                return;
            }

            const guard = new NavigationGuard();
            
            let html = '<h3>Navigation Guard Status:</h3>';
            html += `<div class="status info">📅 Valid Dates: ${guard.hasValidDateSelection() ? '✅ Yes' : '❌ No'}</div>`;
            html += `<div class="status info">🎨 Billboard Type: ${guard.getBillboardType() || '❌ Not set'}</div>`;
            html += `<div class="status info">✅ Design Completed: ${guard.hasCompletedDesign() ? '✅ Yes' : '❌ No'}</div>`;
            
            const paymentResult = guard.validatePageRequirements('payment');
            html += `<div class="status ${paymentResult.isValid ? 'success' : 'error'}">`;
            html += `🔒 Payment Access: ${paymentResult.isValid ? '✅ Allowed' : '❌ Denied'}`;
            if (!paymentResult.isValid) {
                html += `<br>Reason: ${paymentResult.reason}`;
            }
            html += '</div>';
            
            statusDiv.innerHTML = html;
        }

        function simulateCheckoutData() {
            // Simulate the data that would be created during checkout
            const paymentDesignData = {
                billboardType: 'custom',
                designData: { elements: ['text', 'image'] },
                canvasImageData: 'data:image/png;base64,simulated',
                capturedAt: new Date().toISOString()
            };

            const checkoutOrderData = {
                billboardType: 'custom',
                selectedDates: ['2024-01-15', '2024-01-16'],
                customerName: 'Test User',
                customerEmail: '<EMAIL>',
                termsAccepted: true,
                termsAcceptedAt: new Date().toISOString()
            };

            localStorage.setItem('paymentDesignData', JSON.stringify(paymentDesignData));
            localStorage.setItem('checkoutOrderData', JSON.stringify(checkoutOrderData));

            // Also set up order data manager data
            const orderData = {
                selectedDates: ['2024-01-15', '2024-01-16'],
                billboardType: 'custom',
                calendarConfirmed: true,
                designCompleted: true,
                checkoutInitiated: true
            };
            localStorage.setItem('billboardOrderData', JSON.stringify(orderData));

            // Legacy data
            localStorage.setItem('selectedBillboardDates', JSON.stringify(['2024-01-15', '2024-01-16']));
            localStorage.setItem('calendarSelectionConfirmed', 'true');
            localStorage.setItem('billboardType', 'custom');

            document.getElementById('currentStatus').innerHTML = '<div class="status success">✅ Simulated checkout data created</div>';
        }

        function clearAllData() {
            localStorage.clear();
            document.getElementById('currentStatus').innerHTML = '<div class="status info">🧹 All data cleared</div>';
        }

        function testPaymentAccess() {
            const resultDiv = document.getElementById('paymentTestResult');
            
            if (typeof NavigationGuard === 'undefined') {
                resultDiv.innerHTML = '<div class="status error">❌ NavigationGuard class not available</div>';
                return;
            }

            const guard = new NavigationGuard();
            const result = guard.validatePageRequirements('payment');
            
            let html = '<h3>Payment Page Access Test:</h3>';
            html += `<div class="status ${result.isValid ? 'success' : 'error'}">`;
            html += `Result: ${result.isValid ? '✅ ACCESS ALLOWED' : '❌ ACCESS DENIED'}`;
            if (!result.isValid) {
                html += `<br>Reason: ${result.reason}`;
            }
            html += '</div>';
            
            resultDiv.innerHTML = html;
        }

        function showLocalStorageData() {
            const dataDiv = document.getElementById('localStorageData');
            
            const relevantKeys = [
                'paymentDesignData',
                'checkoutOrderData', 
                'billboardOrderData',
                'selectedBillboardDates',
                'calendarSelectionConfirmed',
                'billboardType'
            ];
            
            let html = '<h3>Relevant LocalStorage Data:</h3>';
            
            relevantKeys.forEach(key => {
                const value = localStorage.getItem(key);
                html += `<div class="status ${value ? 'success' : 'error'}">`;
                html += `<strong>${key}:</strong> ${value ? '✅ Present' : '❌ Missing'}`;
                if (value) {
                    try {
                        const parsed = JSON.parse(value);
                        html += `<pre>${JSON.stringify(parsed, null, 2)}</pre>`;
                    } catch (e) {
                        html += `<pre>${value}</pre>`;
                    }
                }
                html += '</div>';
            });
            
            dataDiv.innerHTML = html;
        }

        function setupValidSession() {
            // Set up minimal valid session data
            const today = new Date();
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);
            
            const selectedDates = [today.toISOString().split('T')[0], tomorrow.toISOString().split('T')[0]];
            
            // Order data manager format
            const orderData = {
                selectedDates: selectedDates,
                billboardType: 'custom',
                calendarConfirmed: true,
                designCompleted: true,
                checkoutInitiated: true
            };
            localStorage.setItem('billboardOrderData', JSON.stringify(orderData));
            
            // Legacy format
            localStorage.setItem('selectedBillboardDates', JSON.stringify(selectedDates));
            localStorage.setItem('calendarSelectionConfirmed', 'true');
            localStorage.setItem('billboardType', 'custom');
            
            document.getElementById('setupResult').innerHTML = '<div class="status success">✅ Valid session data setup complete</div>';
        }

        function setupCheckoutData() {
            // Set up checkout completion data
            const checkoutData = {
                customerName: 'Test User',
                customerEmail: '<EMAIL>',
                termsAccepted: true,
                termsAcceptedAt: new Date().toISOString()
            };
            localStorage.setItem('checkoutOrderData', JSON.stringify(checkoutData));
            
            const designData = {
                billboardType: 'custom',
                designData: { test: true },
                canvasImageData: 'test-data'
            };
            localStorage.setItem('paymentDesignData', JSON.stringify(designData));
            
            document.getElementById('setupResult').innerHTML = '<div class="status success">✅ Checkout data setup complete</div>';
        }

        // Auto-check status on load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkCurrentStatus, 500);
        });
    </script>
</body>
</html>
