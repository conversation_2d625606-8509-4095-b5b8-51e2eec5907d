<?php
/**
 * Design Data Compressor
 * Handles compression and decompression of large design data to work within MySQL packet limits
 */

class DesignDataCompressor {
    
    // Maximum uncompressed size before we compress (1MB to work with current MySQL settings)
    const MAX_UNCOMPRESSED_SIZE = 1048576;

    // Maximum compressed size that should fit in 1MB MySQL packet limit (800KB with overhead)
    const MAX_COMPRESSED_SIZE = 819200;
    
    /**
     * Compress design data if it's too large
     */
    public static function compressIfNeeded($designData) {
        if (!$designData) {
            return [
                'data' => null,
                'compressed' => false,
                'original_size' => 0,
                'final_size' => 0
            ];
        }
        
        $jsonData = is_string($designData) ? $designData : json_encode($designData);
        $originalSize = strlen($jsonData);
        
        // If data is small enough, don't compress
        if ($originalSize <= self::MAX_UNCOMPRESSED_SIZE) {
            return [
                'data' => $jsonData,
                'compressed' => false,
                'original_size' => $originalSize,
                'final_size' => $originalSize
            ];
        }
        
        // Try compression
        $compressed = gzcompress($jsonData, 9); // Maximum compression
        $compressedSize = strlen($compressed);
        
        // If compression doesn't help enough, try to reduce the data
        if ($compressedSize > self::MAX_COMPRESSED_SIZE) {
            error_log("Design data too large even after compression: {$compressedSize} bytes");
            
            // Try to reduce the data by removing non-essential parts
            $reducedData = self::reduceDesignData($designData);
            $reducedJson = json_encode($reducedData);
            $reducedCompressed = gzcompress($reducedJson, 9);
            
            if (strlen($reducedCompressed) <= self::MAX_COMPRESSED_SIZE) {
                return [
                    'data' => base64_encode($reducedCompressed),
                    'compressed' => true,
                    'original_size' => $originalSize,
                    'final_size' => strlen($reducedCompressed),
                    'reduced' => true
                ];
            } else {
                // Last resort: store only essential data
                $essentialData = self::getEssentialData($designData);
                $essentialJson = json_encode($essentialData);
                $essentialCompressed = gzcompress($essentialJson, 9);
                
                return [
                    'data' => base64_encode($essentialCompressed),
                    'compressed' => true,
                    'original_size' => $originalSize,
                    'final_size' => strlen($essentialCompressed),
                    'essential_only' => true
                ];
            }
        }
        
        return [
            'data' => base64_encode($compressed),
            'compressed' => true,
            'original_size' => $originalSize,
            'final_size' => $compressedSize
        ];
    }
    
    /**
     * Decompress design data
     */
    public static function decompress($compressedData, $isCompressed = false) {
        if (!$compressedData) {
            return null;
        }
        
        if (!$isCompressed) {
            return is_string($compressedData) ? json_decode($compressedData, true) : $compressedData;
        }
        
        try {
            $decoded = base64_decode($compressedData);
            $decompressed = gzuncompress($decoded);
            
            if ($decompressed === false) {
                error_log("Failed to decompress design data");
                return null;
            }
            
            return json_decode($decompressed, true);
        } catch (Exception $e) {
            error_log("Error decompressing design data: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Reduce design data by removing non-essential elements
     */
    private static function reduceDesignData($designData) {
        if (is_string($designData)) {
            $designData = json_decode($designData, true);
        }
        
        if (!is_array($designData)) {
            return $designData;
        }
        
        $reduced = $designData;
        
        // Remove or reduce large data elements
        if (isset($reduced['objects'])) {
            foreach ($reduced['objects'] as &$object) {
                // Remove unnecessary properties that take up space
                unset($object['shadow']);
                unset($object['clipPath']);
                unset($object['filters']);
                
                // Reduce precision of numeric values
                if (isset($object['left'])) $object['left'] = round($object['left'], 2);
                if (isset($object['top'])) $object['top'] = round($object['top'], 2);
                if (isset($object['scaleX'])) $object['scaleX'] = round($object['scaleX'], 3);
                if (isset($object['scaleY'])) $object['scaleY'] = round($object['scaleY'], 3);
                if (isset($object['angle'])) $object['angle'] = round($object['angle'], 2);
                
                // For images, keep only essential data
                if ($object['type'] === 'image') {
                    // Keep only the src and basic positioning
                    $essential = [
                        'type' => $object['type'],
                        'src' => $object['src'] ?? '',
                        'left' => $object['left'] ?? 0,
                        'top' => $object['top'] ?? 0,
                        'width' => $object['width'] ?? 0,
                        'height' => $object['height'] ?? 0,
                        'scaleX' => $object['scaleX'] ?? 1,
                        'scaleY' => $object['scaleY'] ?? 1,
                        'angle' => $object['angle'] ?? 0
                    ];
                    $object = $essential;
                }
            }
        }
        
        return $reduced;
    }
    
    /**
     * Get only essential data for image generation
     */
    private static function getEssentialData($designData) {
        if (is_string($designData)) {
            $designData = json_decode($designData, true);
        }
        
        if (!is_array($designData)) {
            return ['error' => 'Invalid design data'];
        }
        
        $essential = [
            'version' => $designData['version'] ?? '1.0',
            'width' => $designData['width'] ?? 800,
            'height' => $designData['height'] ?? 400,
            'background' => $designData['background'] ?? '#ffffff',
            'objects' => []
        ];
        
        // Keep only the most important objects
        if (isset($designData['objects'])) {
            foreach ($designData['objects'] as $object) {
                if ($object['type'] === 'text' || $object['type'] === 'image') {
                    $essentialObject = [
                        'type' => $object['type'],
                        'left' => round($object['left'] ?? 0, 1),
                        'top' => round($object['top'] ?? 0, 1),
                        'width' => round($object['width'] ?? 0, 1),
                        'height' => round($object['height'] ?? 0, 1)
                    ];
                    
                    if ($object['type'] === 'text') {
                        $essentialObject['text'] = $object['text'] ?? '';
                        $essentialObject['fontSize'] = $object['fontSize'] ?? 20;
                        $essentialObject['fill'] = $object['fill'] ?? '#000000';
                    } elseif ($object['type'] === 'image') {
                        $essentialObject['src'] = $object['src'] ?? '';
                    }
                    
                    $essential['objects'][] = $essentialObject;
                }
            }
        }
        
        return $essential;
    }
    
    /**
     * Store design data with compression
     */
    public static function storeDesignData($pdo, $paymentIntentId, $designData) {
        $compressed = self::compressIfNeeded($designData);

        // If data is still too large, try more aggressive compression
        if ($compressed['final_size'] > 800000) { // 800KB safety margin
            error_log("Design data still too large ({$compressed['final_size']} bytes), trying essential data only");
            $essentialData = self::getEssentialData($designData);
            $essentialJson = json_encode($essentialData);
            $essentialCompressed = gzcompress($essentialJson, 9);

            $compressed = [
                'data' => base64_encode($essentialCompressed),
                'compressed' => true,
                'original_size' => $compressed['original_size'],
                'final_size' => strlen($essentialCompressed),
                'essential_only' => true
            ];
        }

        $stmt = $pdo->prepare("
            INSERT INTO design_data (payment_intent_id, design_data, data_size_bytes, compression_used)
            VALUES (?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
                design_data = VALUES(design_data),
                data_size_bytes = VALUES(data_size_bytes),
                compression_used = VALUES(compression_used),
                updated_at = CURRENT_TIMESTAMP
        ");

        try {
            $stmt->execute([
                $paymentIntentId,
                $compressed['data'],
                $compressed['final_size'],
                $compressed['compressed']
            ]);

            error_log("Design data stored: Original {$compressed['original_size']} bytes, Final {$compressed['final_size']} bytes, Compressed: " . ($compressed['compressed'] ? 'Yes' : 'No'));

        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'max_allowed_packet') !== false) {
                error_log("MySQL packet size error, trying to store essential data only");

                // Last resort: store only the most essential data
                $minimalData = [
                    'billboardType' => $designData['billboardType'] ?? 'custom',
                    'canvasImageData' => substr($designData['canvasImageData'] ?? '', 0, 100000), // Truncate to 100KB
                    'capturedAt' => $designData['capturedAt'] ?? date('c'),
                    'qualitySettings' => $designData['qualitySettings'] ?? []
                ];

                $minimalJson = json_encode($minimalData);
                $minimalCompressed = gzcompress($minimalJson, 9);

                $stmt->execute([
                    $paymentIntentId,
                    base64_encode($minimalCompressed),
                    strlen($minimalCompressed),
                    true
                ]);

                error_log("Stored minimal design data: " . strlen($minimalCompressed) . " bytes");

                $compressed['final_size'] = strlen($minimalCompressed);
                $compressed['minimal_only'] = true;
            } else {
                throw $e;
            }
        }

        return $compressed;
    }
    
    /**
     * Retrieve design data with decompression
     */
    public static function retrieveDesignData($pdo, $paymentIntentId) {
        $stmt = $pdo->prepare("SELECT design_data, compression_used FROM design_data WHERE payment_intent_id = ?");
        $stmt->execute([$paymentIntentId]);
        $result = $stmt->fetch();
        
        if (!$result) {
            return null;
        }
        
        return self::decompress($result['design_data'], $result['compression_used']);
    }
}
?>
