<?php
// Image Generation Retry Handler
// Handles retries and fallbacks for failed image generation attempts

require_once dirname(__DIR__, 2) . '/config/database.php';
require_once 'fabric-post-payment-generator.php';
require_once 'high-quality-post-payment-generator.php';
require_once 'image-generator.php';
require_once 'image-quality-validator.php';

class ImageGenerationRetryHandler {
    private $pdo;
    private $maxRetries = 3;
    private $retryDelays = [1, 5, 15]; // seconds between retries
    
    public function __construct() {
        $this->pdo = getDBConnection();
    }
    
    /**
     * Attempt image generation with retries and fallbacks (now includes Fabric.js)
     */
    public function generateWithRetries($orderId) {
        $attempts = [];
        $finalResult = null;

        try {
            // Attempt 1: Fabric.js post-payment generation (highest priority)
            $attempts[] = $this->attemptFabricGeneration($orderId, 1);
            if ($attempts[0]['success']) {
                $finalResult = $attempts[0];
            }

            // Attempt 2: High-quality generation
            if (!$finalResult) {
                sleep($this->retryDelays[0]);
                $attempts[] = $this->attemptHighQualityGeneration($orderId, 2);
                if ($attempts[1]['success']) {
                    $finalResult = $attempts[1];
                }
            }

            // Attempt 3: Retry high-quality if previous attempt failed
            if (!$finalResult && $this->shouldRetry($attempts[1] ?? $attempts[0])) {
                sleep($this->retryDelays[0]);
                $attempts[] = $this->attemptHighQualityGeneration($orderId, 3);
                if ($attempts[2]['success']) {
                    $finalResult = $attempts[2];
                }
            }

            // Attempt 4: Fallback to basic generation
            if (!$finalResult) {
                sleep($this->retryDelays[1]);
                $attempts[] = $this->attemptBasicGeneration($orderId, 4);
                if ($attempts[3]['success']) {
                    $finalResult = $attempts[3];
                }
            }

            // Attempt 5: Final fallback - create emergency placeholder
            if (!$finalResult) {
                sleep($this->retryDelays[2]);
                $attempts[] = $this->attemptEmergencyPlaceholder($orderId, 5);
                if ($attempts[4]['success']) {
                    $finalResult = $attempts[4];
                }
            }
            
            // Log all attempts
            $this->logGenerationAttempts($orderId, $attempts);
            
            if ($finalResult) {
                return [
                    'success' => true,
                    'result' => $finalResult,
                    'attempts' => count($attempts),
                    'final_method' => $finalResult['method'],
                    'all_attempts' => $attempts
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'All generation attempts failed',
                    'attempts' => count($attempts),
                    'all_attempts' => $attempts
                ];
            }
            
        } catch (Exception $e) {
            error_log("Image generation retry handler failed for order {$orderId}: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'attempts' => count($attempts),
                'all_attempts' => $attempts
            ];
        }
    }
    
    /**
     * Attempt Fabric.js post-payment generation
     */
    private function attemptFabricGeneration($orderId, $attemptNumber) {
        $startTime = microtime(true);

        try {
            error_log("Attempting Fabric.js generation for order {$orderId} (attempt {$attemptNumber})");
            $result = generateFabricPostPaymentImage($orderId);

            $duration = microtime(true) - $startTime;

            if ($result['success']) {
                error_log("Fabric.js generation succeeded for order {$orderId} in {$duration}s");
                return [
                    'success' => true,
                    'method' => $result['method'],
                    'image_path' => $result['image_path'],
                    'message' => $result['message'],
                    'attempt_number' => $attemptNumber,
                    'duration_seconds' => $duration,
                    'timestamp' => date('Y-m-d H:i:s')
                ];
            } else {
                error_log("Fabric.js generation failed for order {$orderId}: " . $result['error']);
                return [
                    'success' => false,
                    'method' => 'fabric_failed',
                    'error' => $result['error'],
                    'attempt_number' => $attemptNumber,
                    'duration_seconds' => $duration,
                    'timestamp' => date('Y-m-d H:i:s')
                ];
            }

        } catch (Exception $e) {
            $duration = microtime(true) - $startTime;
            error_log("Fabric.js generation exception for order {$orderId}: " . $e->getMessage());

            return [
                'success' => false,
                'method' => 'fabric_exception',
                'error' => $e->getMessage(),
                'attempt_number' => $attemptNumber,
                'duration_seconds' => $duration,
                'timestamp' => date('Y-m-d H:i:s')
            ];
        }
    }

    /**
     * Attempt high-quality generation
     */
    private function attemptHighQualityGeneration($orderId, $attemptNumber) {
        $startTime = microtime(true);

        try {
            $generator = new HighQualityPostPaymentGenerator($orderId);
            $result = $generator->generateHighQualityImage();
            
            $processingTime = round((microtime(true) - $startTime) * 1000);
            
            if ($result['success']) {
                // Validate the generated image
                $validator = new ImageQualityValidator();
                $validation = $validator->validateImage($result['image_path'], 'high_quality');
                
                return [
                    'success' => $validation['valid'],
                    'method' => 'high_quality',
                    'attempt_number' => $attemptNumber,
                    'image_path' => $result['image_path'],
                    'processing_time_ms' => $processingTime,
                    'validation' => $validation,
                    'error' => $validation['valid'] ? null : 'Quality validation failed',
                    'details' => $result
                ];
            } else {
                return [
                    'success' => false,
                    'method' => 'high_quality',
                    'attempt_number' => $attemptNumber,
                    'processing_time_ms' => $processingTime,
                    'error' => $result['error'] ?? 'High-quality generation failed',
                    'details' => $result
                ];
            }
            
        } catch (Exception $e) {
            $processingTime = round((microtime(true) - $startTime) * 1000);
            return [
                'success' => false,
                'method' => 'high_quality',
                'attempt_number' => $attemptNumber,
                'processing_time_ms' => $processingTime,
                'error' => $e->getMessage(),
                'exception' => true
            ];
        }
    }
    
    /**
     * Attempt basic generation
     */
    private function attemptBasicGeneration($orderId, $attemptNumber) {
        $startTime = microtime(true);
        
        try {
            $generator = new BillboardImageGenerator($orderId);
            $result = $generator->generateImage();
            
            $processingTime = round((microtime(true) - $startTime) * 1000);
            
            if ($result['success']) {
                // Validate with basic standards
                $validator = new ImageQualityValidator();
                $validation = $validator->validateImage($result['image_path'], 'basic');
                
                return [
                    'success' => $validation['valid'],
                    'method' => 'basic',
                    'attempt_number' => $attemptNumber,
                    'image_path' => $result['image_path'],
                    'processing_time_ms' => $processingTime,
                    'validation' => $validation,
                    'error' => $validation['valid'] ? null : 'Basic quality validation failed',
                    'details' => $result
                ];
            } else {
                return [
                    'success' => false,
                    'method' => 'basic',
                    'attempt_number' => $attemptNumber,
                    'processing_time_ms' => $processingTime,
                    'error' => $result['error'] ?? 'Basic generation failed',
                    'details' => $result
                ];
            }
            
        } catch (Exception $e) {
            $processingTime = round((microtime(true) - $startTime) * 1000);
            return [
                'success' => false,
                'method' => 'basic',
                'attempt_number' => $attemptNumber,
                'processing_time_ms' => $processingTime,
                'error' => $e->getMessage(),
                'exception' => true
            ];
        }
    }
    
    /**
     * Create emergency placeholder as final fallback
     */
    private function attemptEmergencyPlaceholder($orderId, $attemptNumber) {
        $startTime = microtime(true);
        
        try {
            // Get order information
            $stmt = $this->pdo->prepare("SELECT * FROM orders WHERE id = ?");
            $stmt->execute([$orderId]);
            $order = $stmt->fetch();
            
            if (!$order) {
                throw new Exception("Order not found: {$orderId}");
            }
            
            // Create emergency directory in the uploads structure
            $customerEmail = $order['customer_email'];
            $customerHash = substr(md5($customerEmail), 0, 8);
            $emergencyDir = dirname(__DIR__, 2) . '/uploads/billboards/customer-' . $customerHash;
            if (!is_dir($emergencyDir)) {
                mkdir($emergencyDir, 0755, true);
            }
            
            // Generate emergency placeholder with standard naming
            $timestamp = date('Y-m-d_H-i-s');
            $randomId = substr(uniqid(), -8);
            $filename = "billboard_emergency_{$order['order_number']}_{$timestamp}_{$randomId}.png";
            $imagePath = $emergencyDir . '/' . $filename;
            
            $image = imagecreatetruecolor(1600, 800); // Minimum acceptable size
            
            // Set background
            $backgroundColor = imagecolorallocate($image, 255, 255, 255);
            imagefill($image, 0, 0, $backgroundColor);
            
            // Add border
            $borderColor = imagecolorallocate($image, 200, 200, 200);
            imagerectangle($image, 5, 5, 1595, 795, $borderColor);
            
            // Add text
            $textColor = imagecolorallocate($image, 60, 60, 60);
            imagestring($image, 5, 50, 50, "EMERGENCY BILLBOARD PLACEHOLDER", $textColor);
            imagestring($image, 4, 50, 80, "Order: " . $order['order_number'], $textColor);
            imagestring($image, 4, 50, 110, "Customer: " . $order['customer_name'], $textColor);
            imagestring($image, 3, 50, 140, "Generated: " . date('Y-m-d H:i:s'), $textColor);
            imagestring($image, 3, 50, 170, "This is a temporary placeholder.", $textColor);
            imagestring($image, 3, 50, 200, "Please contact support for assistance.", $textColor);
            
            // Save image
            if (!imagepng($image, $imagePath, 0)) {
                imagedestroy($image);
                throw new Exception('Failed to save emergency placeholder');
            }
            
            imagedestroy($image);
            
            // Save to database
            $this->saveEmergencyImageRecord($orderId, $imagePath, $filename);
            
            $processingTime = round((microtime(true) - $startTime) * 1000);
            
            return [
                'success' => true,
                'method' => 'emergency_placeholder',
                'attempt_number' => $attemptNumber,
                'image_path' => $imagePath,
                'processing_time_ms' => $processingTime,
                'message' => 'Emergency placeholder created successfully',
                'is_emergency' => true
            ];
            
        } catch (Exception $e) {
            $processingTime = round((microtime(true) - $startTime) * 1000);
            return [
                'success' => false,
                'method' => 'emergency_placeholder',
                'attempt_number' => $attemptNumber,
                'processing_time_ms' => $processingTime,
                'error' => $e->getMessage(),
                'exception' => true
            ];
        }
    }
    
    /**
     * Determine if we should retry based on the error
     */
    private function shouldRetry($attempt) {
        if ($attempt['success']) {
            return false;
        }
        
        // Don't retry if it's a validation failure (image was generated but quality is poor)
        if (isset($attempt['validation']) && !$attempt['validation']['valid']) {
            return false;
        }
        
        // Don't retry certain types of errors
        $nonRetryableErrors = [
            'Order not found',
            'Payment not completed',
            'Invalid design data format'
        ];
        
        foreach ($nonRetryableErrors as $errorPattern) {
            if (strpos($attempt['error'] ?? '', $errorPattern) !== false) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Save emergency image record to database
     */
    private function saveEmergencyImageRecord($orderId, $imagePath, $filename) {
        $fileSize = filesize($imagePath);
        $imageInfo = getimagesize($imagePath);
        
        $stmt = $this->pdo->prepare("
            INSERT INTO billboard_images (
                order_id, image_filename, image_path, image_size_bytes,
                image_width, image_height, image_format, quality_type,
                is_post_payment, validation_status, generation_method
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $orderId,
            $filename,
            $imagePath,
            $fileSize,
            $imageInfo[0],
            $imageInfo[1],
            'png',
            'basic',
            true,
            'skipped',
            'emergency_placeholder'
        ]);
        
        // Update order
        try {
            $stmt = $this->pdo->prepare("
                UPDATE orders
                SET billboard_image_path = ?, image_generated_at = NOW(), image_generation_method = ?
                WHERE id = ?
            ");
            $stmt->execute([$imagePath, 'fallback', $orderId]);
        } catch (PDOException $e) {
            // If image_generation_method column doesn't exist, update without it
            if (strpos($e->getMessage(), 'image_generation_method') !== false) {
                error_log("image_generation_method column not found, updating without it");
                $stmt = $this->pdo->prepare("
                    UPDATE orders
                    SET billboard_image_path = ?, image_generated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$imagePath, $orderId]);
            } else {
                throw $e;
            }
        }
    }
    
    /**
     * Log all generation attempts
     */
    private function logGenerationAttempts($orderId, $attempts) {
        foreach ($attempts as $attempt) {
            $stmt = $this->pdo->prepare("
                INSERT INTO image_generation_logs (
                    order_id, attempt_number, method, generation_type, status,
                    error_message, image_path, file_size_bytes,
                    image_dimensions, processing_time_ms
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $status = $attempt['success'] ? 'completed' : 'failed';
            $dimensions = null;
            $fileSize = null;

            if ($attempt['success'] && isset($attempt['image_path'])) {
                $imageInfo = getimagesize($attempt['image_path']);
                if ($imageInfo) {
                    $dimensions = $imageInfo[0] . 'x' . $imageInfo[1];
                    $fileSize = filesize($attempt['image_path']);
                }
            }

            $stmt->execute([
                $orderId,
                $attempt['attempt_number'],
                $attempt['method'] ?? 'unknown',
                $attempt['generation_type'] ?? $attempt['method'] ?? 'unknown',
                $status,
                $attempt['error'] ?? null,
                $attempt['image_path'] ?? null,
                $fileSize,
                $dimensions,
                $attempt['processing_time_ms'] ?? null
            ]);
        }
    }
}
