// ========================================
// NAVIGATION GUARD UTILITY MODULE
// ========================================

/**
 * Navigation Guard System
 * Protects restricted pages by ensuring users have completed required steps
 * before accessing design or payment pages.
 */
class NavigationGuard {
    constructor() {
        // Determine the correct entry page URL based on current location
        this.entryPageUrl = this.getEntryPageUrl();
        this.init();
    }

    /**
     * Get the correct entry page URL based on current location
     * @returns {string} Relative URL to the entry page
     */
    getEntryPageUrl() {
        const currentPath = window.location.pathname;

        if (currentPath.includes('fabric-templated-billboard') || currentPath.includes('fabric-custom-billboard')) {
            return '../index.php';
        } else if (currentPath.includes('shared/payment')) {
            return '../../index.php';
        } else if (currentPath.includes('/customer/')) {
            return 'index.php';
        }

        // Default fallback
        return '/customer/index.php';
    }

    init() {
        // Check if we're on a restricted page and validate access
        this.validatePageAccess();
    }

    /**
     * Check if user has selected valid dates
     * @returns {boolean} True if dates are selected and confirmed
     */
    hasValidDateSelection() {
        try {
            // Check both new order data manager and legacy localStorage
            const orderData = this.getOrderData();
            const legacyDates = localStorage.getItem('selectedBillboardDates');
            const legacyConfirmed = localStorage.getItem('calendarSelectionConfirmed');

            // Check new order data manager first
            if (orderData && orderData.selectedDates && orderData.selectedDates.length > 0 && orderData.calendarConfirmed) {
                return true;
            }

            // Fallback to legacy localStorage check
            if (legacyDates && legacyConfirmed === 'true') {
                const dates = JSON.parse(legacyDates);
                return dates && dates.length > 0;
            }

            return false;
        } catch (error) {
            console.error('Error checking date selection:', error);
            return false;
        }
    }

    /**
     * Check if user has selected a billboard type
     * @returns {string|null} Billboard type ('templated' or 'custom') or null
     */
    getBillboardType() {
        try {
            // Check new order data manager first
            const orderData = this.getOrderData();
            if (orderData && orderData.billboardType) {
                return orderData.billboardType;
            }

            // Fallback to legacy localStorage
            const legacyType = localStorage.getItem('billboardType');
            return legacyType;
        } catch (error) {
            console.error('Error getting billboard type:', error);
            return null;
        }
    }

    /**
     * Get order data from the order data manager
     * @returns {object|null} Order data object or null
     */
    getOrderData() {
        try {
            const data = localStorage.getItem('billboardOrderData');
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('Error getting order data:', error);
            return null;
        }
    }

    /**
     * Check if user has completed the design process (for payment page)
     * @returns {boolean} True if design is complete
     */
    hasCompletedDesign() {
        try {
            const orderData = this.getOrderData();

            // Check if image has been generated (indicates design completion)
            if (orderData && orderData.imageGenerated) {
                return true;
            }

            // Check if design completion has been explicitly marked
            if (orderData && (orderData.designCompleted || orderData.checkoutInitiated)) {
                return true;
            }

            // Check for checkout process completion indicators
            // 1. Check if payment design data exists (set during checkout)
            const paymentDesignData = localStorage.getItem('paymentDesignData');
            if (paymentDesignData) {
                try {
                    const designData = JSON.parse(paymentDesignData);
                    if (designData && (designData.designData || designData.canvasImageData)) {
                        return true;
                    }
                } catch (e) {
                    console.warn('Error parsing payment design data:', e);
                }
            }

            // 2. Check if checkout order data exists (set during checkout)
            const checkoutOrderData = localStorage.getItem('checkoutOrderData');
            if (checkoutOrderData) {
                try {
                    const orderInfo = JSON.parse(checkoutOrderData);
                    if (orderInfo && orderInfo.termsAccepted) {
                        return true;
                    }
                } catch (e) {
                    console.warn('Error parsing checkout order data:', e);
                }
            }

            // 3. Check if we're coming from a design page (basic fallback)
            const referrer = document.referrer;
            const comingFromDesignPage = referrer && (
                referrer.includes('fabric-templated-billboard') ||
                referrer.includes('fabric-custom-billboard')
            );

            if (comingFromDesignPage) {
                console.log('🔄 Coming from design page, allowing access');
                return true;
            }

            // 4. Fallback: check if we have any design-related data in order manager
            const hasDesignData = orderData && (
                orderData.designData ||
                orderData.templateId ||
                orderData.generatedImageUrl
            );

            return hasDesignData;
        } catch (error) {
            console.error('Error checking design completion:', error);
            return false;
        }
    }

    /**
     * Validate access to the current page
     */
    validatePageAccess() {
        const currentPath = window.location.pathname;
        const currentPage = this.getCurrentPageType(currentPath);

        if (!currentPage) {
            // Not a restricted page, allow access
            return;
        }

        const validationResult = this.validatePageRequirements(currentPage);
        
        if (!validationResult.isValid) {
            this.redirectToEntryPage(validationResult.reason);
        }
    }

    /**
     * Determine the current page type based on URL
     * @param {string} path Current page path
     * @returns {string|null} Page type or null if not restricted
     */
    getCurrentPageType(path) {
        if (path.includes('fabric-templated-billboard')) {
            return 'templated';
        } else if (path.includes('fabric-custom-billboard')) {
            return 'custom';
        } else if (path.includes('stripe-checkout')) {
            return 'payment';
        }
        return null;
    }

    /**
     * Validate requirements for a specific page type
     * @param {string} pageType Type of page ('templated', 'custom', 'payment')
     * @returns {object} Validation result with isValid and reason
     */
    validatePageRequirements(pageType) {
        // All pages require valid date selection
        if (!this.hasValidDateSelection()) {
            return {
                isValid: false,
                reason: 'No valid date selection found. Please select your billboard display dates first.'
            };
        }

        const billboardType = this.getBillboardType();

        switch (pageType) {
            case 'templated':
                if (billboardType !== 'templated') {
                    return {
                        isValid: false,
                        reason: 'Access denied. Please select the templated billboard option first.'
                    };
                }
                break;

            case 'custom':
                if (billboardType !== 'custom') {
                    return {
                        isValid: false,
                        reason: 'Access denied. Please select the custom billboard option first.'
                    };
                }
                break;

            case 'payment':
                // Payment page requires both date selection and completed design
                if (!billboardType) {
                    return {
                        isValid: false,
                        reason: 'Access denied. Please select a billboard type first.'
                    };
                }
                
                if (!this.hasCompletedDesign()) {
                    return {
                        isValid: false,
                        reason: 'Access denied. Please complete your billboard design first.'
                    };
                }
                break;

            default:
                return {
                    isValid: false,
                    reason: 'Unknown page type.'
                };
        }

        return { isValid: true };
    }

    /**
     * Redirect user to entry page with message
     * @param {string} reason Reason for redirection
     */
    redirectToEntryPage(reason) {
        // Add debugging information
        console.error('🚫 Navigation Guard: Access denied');
        console.error('📍 Current path:', window.location.pathname);
        console.error('❌ Reason:', reason);

        // Debug current state
        this.debugCurrentState();

        // Show alert with reason
        alert(reason + '\n\nYou will be redirected to the main page.');

        // Redirect to entry page
        window.location.href = this.entryPageUrl;
    }

    /**
     * Debug current state for troubleshooting
     */
    debugCurrentState() {
        console.log('🔍 Navigation Guard Debug State:');
        console.log('📅 Valid dates:', this.hasValidDateSelection());
        console.log('🎨 Billboard type:', this.getBillboardType());
        console.log('✅ Design completed:', this.hasCompletedDesign());

        const orderData = this.getOrderData();
        console.log('📊 Order data:', orderData);

        const paymentDesignData = localStorage.getItem('paymentDesignData');
        console.log('🎨 Payment design data exists:', !!paymentDesignData);

        const checkoutOrderData = localStorage.getItem('checkoutOrderData');
        console.log('🛒 Checkout order data exists:', !!checkoutOrderData);

        if (checkoutOrderData) {
            try {
                const parsed = JSON.parse(checkoutOrderData);
                console.log('🛒 Checkout data parsed:', parsed);
            } catch (e) {
                console.error('❌ Error parsing checkout data:', e);
            }
        }
    }

    /**
     * Manual validation method for specific requirements
     * @param {string} pageType Page type to validate for
     * @returns {boolean} True if access is allowed
     */
    static validateAccess(pageType) {
        const guard = new NavigationGuard();
        const result = guard.validatePageRequirements(pageType);
        
        if (!result.isValid) {
            guard.redirectToEntryPage(result.reason);
            return false;
        }
        
        return true;
    }
}

// Auto-initialize navigation guard when script loads
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if we're not on the entry page
    const currentPath = window.location.pathname;
    if (!currentPath.includes('/customer/index.php') && !currentPath.endsWith('/customer/')) {
        new NavigationGuard();
    }
});

// Export for manual use
window.NavigationGuard = NavigationGuard;
