// ========================================
// REFRESH PROTECTION UTILITY MODULE
// ========================================

/**
 * Refresh Protection System
 * Prevents accidental page refreshes on design and payment pages
 * to avoid losing unsaved progress or interrupting payment processes.
 */
class RefreshProtection {
    constructor(options = {}) {
        this.options = {
            message: options.message || 'Are you sure you want to refresh this page? Your progress will be lost.',
            enableKeyboardProtection: options.enableKeyboardProtection !== false,
            enableBeforeUnloadProtection: options.enableBeforeUnloadProtection !== false,
            enablePopstateProtection: options.enablePopstateProtection !== false,
            ...options
        };

        this.isEnabled = false;
        this.isNavigating = false; // Flag to track legitimate navigation
        this.init();
    }

    init() {
        this.enable();
    }

    /**
     * Enable refresh protection
     */
    enable() {
        if (this.isEnabled) {
            return;
        }

        this.isEnabled = true;

        // Protect against F5, Ctrl+R, Ctrl+F5
        if (this.options.enableKeyboardProtection) {
            this.addKeyboardProtection();
        }

        // Protect against browser refresh button and window close
        if (this.options.enableBeforeUnloadProtection) {
            this.addBeforeUnloadProtection();
        }

        // Protect against browser back/forward navigation
        if (this.options.enablePopstateProtection) {
            this.addPopstateProtection();
        }

        // Protection enabled silently
    }

    /**
     * Disable refresh protection
     */
    disable() {
        if (!this.isEnabled) {
            return;
        }

        this.isEnabled = false;

        // Remove keyboard protection
        if (this.keyboardHandler) {
            document.removeEventListener('keydown', this.keyboardHandler, { capture: true });
            window.removeEventListener('keydown', this.keyboardHandler, { capture: true });
            this.keyboardHandler = null;
        }

        // Remove beforeunload protection
        if (this.beforeUnloadHandler) {
            window.removeEventListener('beforeunload', this.beforeUnloadHandler);
            this.beforeUnloadHandler = null;
        }

        // Remove popstate protection
        if (this.popstateHandler) {
            window.removeEventListener('popstate', this.popstateHandler);
            this.popstateHandler = null;
        }

        // Protection disabled silently
    }

    /**
     * Add keyboard shortcut protection (F5, Ctrl+R, Ctrl+F5)
     */
    addKeyboardProtection() {
        this.keyboardHandler = (event) => {
            let shouldPrevent = false;

            // F5 key (case insensitive)
            if (event.key === 'F5' || event.keyCode === 116) {
                shouldPrevent = true;
            }

            // Ctrl+R or Cmd+R (Refresh) - handle both lowercase and uppercase
            if ((event.ctrlKey || event.metaKey) && (event.key === 'r' || event.key === 'R' || event.keyCode === 82)) {
                shouldPrevent = true;
            }

            // Ctrl+F5 (Hard refresh)
            if ((event.ctrlKey || event.metaKey) && (event.key === 'F5' || event.keyCode === 116)) {
                shouldPrevent = true;
            }

            // Ctrl+Shift+R (Hard refresh alternative)
            if ((event.ctrlKey || event.metaKey) && event.shiftKey && (event.key === 'R' || event.key === 'r' || event.keyCode === 82)) {
                shouldPrevent = true;
            }

            if (shouldPrevent) {
                if (!this.confirmRefresh()) {
                    event.preventDefault();
                    event.stopPropagation();
                    event.stopImmediatePropagation();
                    return false;
                }
            }
        };

        // Add event listener with capture to catch events early
        document.addEventListener('keydown', this.keyboardHandler, { capture: true, passive: false });

        // Also add to window for broader coverage
        window.addEventListener('keydown', this.keyboardHandler, { capture: true, passive: false });
    }

    /**
     * Add beforeunload protection (browser refresh button, window close)
     */
    addBeforeUnloadProtection() {
        this.beforeUnloadHandler = (event) => {
            // Skip protection if we're in the middle of legitimate navigation
            if (this.isNavigating) {
                return;
            }

            // Skip protection if payment is completed or in progress
            if (window.paymentCompleted || window.paymentInProgress) {
                console.log('🔄 Skipping beforeunload protection - payment completed/in progress');
                return;
            }

            // Skip protection if we're navigating to success page
            if (window.location.href.includes('payment-success') ||
                window.location.href.includes('redirect_status=succeeded')) {
                console.log('🔄 Skipping beforeunload protection - success page navigation');
                return;
            }

            // Modern browsers ignore the custom message and show their own
            // But we still need to set returnValue for the dialog to appear
            const message = this.options.message;

            event.preventDefault();
            event.returnValue = message;

            // For older browsers
            return message;
        };

        window.addEventListener('beforeunload', this.beforeUnloadHandler, { passive: false });
    }

    /**
     * Add popstate protection (browser back/forward buttons)
     */
    addPopstateProtection() {
        // Push a dummy state to prevent immediate back navigation
        if (window.history.state === null) {
            window.history.pushState({ refreshProtection: true }, '', window.location.href);
        }

        this.popstateHandler = (event) => {
            if (this.confirmNavigation()) {
                return; // Allow navigation
            }
            
            // Prevent navigation by pushing the current state back
            window.history.pushState({ refreshProtection: true }, '', window.location.href);
        };

        window.addEventListener('popstate', this.popstateHandler);
    }

    /**
     * Show confirmation dialog for refresh
     * @returns {boolean} True if user confirms refresh
     */
    confirmRefresh() {
        return confirm(this.options.message);
    }

    /**
     * Show confirmation dialog for navigation
     * @returns {boolean} True if user confirms navigation
     */
    confirmNavigation() {
        return confirm('Are you sure you want to leave this page? Your progress will be lost.');
    }

    /**
     * Temporarily disable protection (useful for legitimate form submissions)
     * @param {number} duration Duration in milliseconds to disable protection
     */
    temporaryDisable(duration = 5000) {
        if (!this.isEnabled) {
            return;
        }

        this.disable();
        
        setTimeout(() => {
            this.enable();
        }, duration);

        // Temporarily disabled
    }

    /**
     * Check if protection is currently enabled
     * @returns {boolean} True if protection is enabled
     */
    isProtectionEnabled() {
        return this.isEnabled;
    }

    /**
     * Update protection message
     * @param {string} message New message to display
     */
    updateMessage(message) {
        this.options.message = message;
    }

    /**
     * Allow legitimate navigation without showing warning
     * @param {number} duration Duration in milliseconds to allow navigation (default: 5000)
     */
    allowNavigation(duration = 5000) {
        this.isNavigating = true;

        // Reset the flag after the specified duration
        setTimeout(() => {
            this.isNavigating = false;
        }, duration);
    }

    /**
     * Cancel navigation allowance
     */
    cancelNavigation() {
        this.isNavigating = false;
    }

    /**
     * Static method to create and enable refresh protection
     * @param {object} options Configuration options
     * @returns {RefreshProtection} RefreshProtection instance
     */
    static enable(options = {}) {
        return new RefreshProtection(options);
    }

    /**
     * Static method to disable all refresh protection on the page
     */
    static disableAll() {
        if (window.refreshProtectionInstance) {
            window.refreshProtectionInstance.disable();
            window.refreshProtectionInstance = null;
        }

        // Also remove any standalone beforeunload listeners
        window.onbeforeunload = null;

        // Set global flags to prevent any remaining protection
        window.paymentCompleted = true;

        console.log('🔄 All refresh protection disabled');
    }

    /**
     * Static method specifically for payment completion - completely disables all protection
     */
    static disableForPaymentCompletion() {
        console.log('💳 Disabling all protection for payment completion');

        // Disable instance protection
        if (window.refreshProtectionInstance) {
            window.refreshProtectionInstance.disable();
            window.refreshProtectionInstance = null;
        }

        // Remove all beforeunload listeners
        window.onbeforeunload = null;

        // Set global flags
        window.paymentCompleted = true;
        window.paymentInProgress = true;

        // Remove any additional event listeners that might interfere
        const events = ['beforeunload', 'unload', 'pagehide'];
        events.forEach(eventType => {
            // Create a new event listener that does nothing
            const emptyHandler = () => {};
            window.addEventListener(eventType, emptyHandler);

            // Immediately remove it to clear any existing listeners
            setTimeout(() => {
                window.removeEventListener(eventType, emptyHandler);
            }, 100);
        });

        console.log('✅ Payment completion protection disabled');
    }

    /**
     * Static method to allow navigation without warnings
     * @param {number} duration Duration in milliseconds to allow navigation
     */
    static allowNavigation(duration = 5000) {
        if (window.refreshProtectionInstance && window.refreshProtectionInstance.isProtectionEnabled()) {
            window.refreshProtectionInstance.allowNavigation(duration);
        }
    }

    /**
     * Static method to safely navigate to a URL without beforeunload warnings
     * @param {string} url URL to navigate to
     * @param {number} delay Delay before navigation (default: 50ms)
     */
    static safeNavigate(url, delay = 50) {
        this.allowNavigation(3000);
        setTimeout(() => {
            window.location.href = url;
        }, delay);
    }

    /**
     * Static method specifically for payment completion scenarios
     * Disables protection for longer duration to handle Stripe redirects
     * @param {number} duration Duration in milliseconds (default: 15000ms)
     */
    static allowPaymentCompletion(duration = 15000) {
        if (window.refreshProtectionInstance && window.refreshProtectionInstance.isProtectionEnabled()) {
            console.log('💳 Allowing payment completion navigation for', duration, 'ms');
            window.refreshProtectionInstance.allowNavigation(duration);
        }
    }

    /**
     * Enhanced protection specifically for payment pages
     * Includes additional safeguards for payment processing
     * @param {object} options Configuration options
     * @returns {RefreshProtection} RefreshProtection instance
     */
    static enablePaymentProtection(options = {}) {
        const paymentOptions = {
            message: 'Are you sure you want to refresh this page? Your payment process will be interrupted and you may need to start over.',
            enableKeyboardProtection: true,
            enableBeforeUnloadProtection: true,
            enablePopstateProtection: true,
            ...options
        };

        const instance = new RefreshProtection(paymentOptions);

        // Add payment-specific enhancements
        instance.addPaymentSpecificProtection();

        return instance;
    }

    /**
     * Add payment-specific protection enhancements
     */
    addPaymentSpecificProtection() {
        // Disable browser context menu to prevent right-click refresh
        document.addEventListener('contextmenu', function(e) {
            const isPaymentArea = e.target.closest('.payment-container') ||
                                 e.target.closest('#payment-element') ||
                                 e.target.closest('#payment-form');

            if (isPaymentArea) {
                e.preventDefault();
                return false;
            }
        });

        // Monitor for payment form interactions
        const paymentForm = document.getElementById('payment-form');
        if (paymentForm) {
            paymentForm.addEventListener('submit', () => {
                this.updateMessage('Payment is being processed. Please do not refresh or close this page.');
            });
        }

        // Add visual indicator when protection is active
        this.addProtectionIndicator();
    }

    /**
     * Add visual indicator that refresh protection is active
     */
    addProtectionIndicator() {
        if (document.querySelector('.refresh-protection-indicator')) {
            return; // Already exists
        }

        const indicator = document.createElement('div');
        indicator.className = 'refresh-protection-indicator';
        indicator.innerHTML = `
            <i class="fas fa-shield-alt"></i>
            <span>Page protected</span>
        `;
        indicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(40, 167, 69, 0.9);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            transition: opacity 0.3s ease;
        `;

        document.body.appendChild(indicator);

        // Auto-hide after 3 seconds
        setTimeout(() => {
            indicator.style.opacity = '0';
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.parentNode.removeChild(indicator);
                }
            }, 300);
        }, 3000);
    }
}

// Auto-initialize refresh protection for design and payment pages
document.addEventListener('DOMContentLoaded', function() {
    const currentPath = window.location.pathname;

    // Check if we're on a page that needs refresh protection
    const needsProtection =
        currentPath.includes('fabric-templated-billboard') ||
        currentPath.includes('fabric-custom-billboard') ||
        currentPath.includes('stripe-checkout');

    if (needsProtection && !window.refreshProtectionInstance) {
        // Use enhanced payment protection for payment pages
        if (currentPath.includes('stripe-checkout')) {
            window.refreshProtectionInstance = RefreshProtection.enablePaymentProtection({
                message: 'Are you sure you want to refresh this page? Your payment process will be interrupted and you may need to start over.'
            });
            console.log('🛡️ Enhanced payment protection enabled');
        } else {
            // Standard protection for design pages
            window.refreshProtectionInstance = RefreshProtection.enable({
                message: 'Are you sure you want to refresh this page? Your progress will be lost.'
            });
            console.log('🛡️ Standard refresh protection enabled');
        }
    }
});

// Export for manual use
window.RefreshProtection = RefreshProtection;
