<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Guards Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🛡️ Navigation Guards & Refresh Protection Test</h1>
    
    <div class="test-section">
        <h2>📋 Test Instructions</h2>
        <p>This page helps you test the navigation guards and refresh protection functionality.</p>
        <ol>
            <li>First, clear all localStorage data using the button below</li>
            <li>Try accessing restricted pages directly - you should be redirected</li>
            <li>Set up valid session data and try again - access should be allowed</li>
            <li>Test refresh protection on design pages</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🧹 Clear Session Data</h2>
        <button class="test-button" onclick="clearAllData()">Clear All localStorage Data</button>
        <div id="clearStatus"></div>
    </div>

    <div class="test-section">
        <h2>📅 Set Valid Date Selection</h2>
        <button class="test-button" onclick="setValidDates()">Set Valid Date Selection</button>
        <div id="dateStatus"></div>
    </div>

    <div class="test-section">
        <h2>🎨 Set Billboard Type</h2>
        <button class="test-button" onclick="setBillboardType('templated')">Set Templated Type</button>
        <button class="test-button" onclick="setBillboardType('custom')">Set Custom Type</button>
        <div id="typeStatus"></div>
    </div>

    <div class="test-section">
        <h2>🔗 Test Direct Access (Should Redirect)</h2>
        <p>These links should redirect you to the main page if session data is not set:</p>
        <a href="../fabric-templated-billboard/index.php" target="_blank" class="test-button">Templated Billboard Page</a>
        <a href="../fabric-custom-billboard/index.php" target="_blank" class="test-button">Custom Billboard Page</a>
        <a href="payment/stripe-checkout.php" target="_blank" class="test-button">Payment Page</a>
    </div>

    <div class="test-section">
        <h2>📊 Current Session Status</h2>
        <button class="test-button" onclick="checkSessionStatus()">Check Current Status</button>
        <div id="sessionStatus"></div>
    </div>

    <div class="test-section">
        <h2>🔄 Test Refresh Protection</h2>
        <p>After setting valid session data, visit a design page and try:</p>
        <ul>
            <li>Press F5 to refresh</li>
            <li>Press Ctrl+R to refresh</li>
            <li>Click browser refresh button</li>
            <li>Try to close the tab/window</li>
        </ul>
        <p>You should see confirmation dialogs asking if you want to lose your progress.</p>
    </div>

    <script src="navigation-guard.js"></script>
    <script>
        function clearAllData() {
            localStorage.clear();
            showStatus('clearStatus', 'All localStorage data cleared!', 'success');
        }

        function setValidDates() {
            const today = new Date();
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);
            
            const selectedDates = [today.toISOString().split('T')[0], tomorrow.toISOString().split('T')[0]];
            
            // Set in both new and legacy formats
            const orderData = {
                selectedDates: selectedDates,
                bookingStartDate: selectedDates[0],
                bookingEndDate: selectedDates[1],
                calendarConfirmed: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            
            localStorage.setItem('billboardOrderData', JSON.stringify(orderData));
            localStorage.setItem('selectedBillboardDates', JSON.stringify(selectedDates));
            localStorage.setItem('bookingStartDate', selectedDates[0]);
            localStorage.setItem('bookingEndDate', selectedDates[1]);
            localStorage.setItem('calendarSelectionConfirmed', 'true');
            
            showStatus('dateStatus', `Valid dates set: ${selectedDates.join(' to ')}`, 'success');
        }

        function setBillboardType(type) {
            // Set in both new and legacy formats
            const orderData = JSON.parse(localStorage.getItem('billboardOrderData') || '{}');
            orderData.billboardType = type;
            orderData.updatedAt = new Date().toISOString();
            
            localStorage.setItem('billboardOrderData', JSON.stringify(orderData));
            localStorage.setItem('billboardType', type);
            
            showStatus('typeStatus', `Billboard type set to: ${type}`, 'success');
        }

        function checkSessionStatus() {
            const guard = new NavigationGuard();
            const hasValidDates = guard.hasValidDateSelection();
            const billboardType = guard.getBillboardType();
            const hasCompletedDesign = guard.hasCompletedDesign();
            
            let status = '<div class="code">';
            status += `Valid Dates: ${hasValidDates ? '✅ Yes' : '❌ No'}<br>`;
            status += `Billboard Type: ${billboardType ? '✅ ' + billboardType : '❌ Not set'}<br>`;
            status += `Design Completed: ${hasCompletedDesign ? '✅ Yes' : '❌ No'}<br>`;
            status += '</div>';
            
            status += '<h4>Access Permissions:</h4><div class="code">';
            const templatedResult = guard.validatePageRequirements('templated');
            const customResult = guard.validatePageRequirements('custom');
            const paymentResult = guard.validatePageRequirements('payment');
            
            status += `Templated Page: ${templatedResult.isValid ? '✅ Allowed' : '❌ ' + templatedResult.reason}<br>`;
            status += `Custom Page: ${customResult.isValid ? '✅ Allowed' : '❌ ' + customResult.reason}<br>`;
            status += `Payment Page: ${paymentResult.isValid ? '✅ Allowed' : '❌ ' + paymentResult.reason}<br>`;
            status += '</div>';
            
            document.getElementById('sessionStatus').innerHTML = status;
        }

        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // Initialize status check
        document.addEventListener('DOMContentLoaded', function() {
            checkSessionStatus();
        });
    </script>
</body>
</html>
