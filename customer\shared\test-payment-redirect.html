<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Redirect Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .test-button.success {
            background: #28a745;
        }
        
        .test-button.success:hover {
            background: #218838;
        }
        
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border-color: #bee5eb;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-credit-card"></i> Payment Redirect Test</h1>
        <p>This page tests the refresh protection behavior during payment completion and redirects.</p>
        
        <div class="status info">
            <strong>Test Scenario:</strong> Simulate payment completion and test if redirect warnings appear.
        </div>
        
        <div>
            <h3>Step 1: Enable Protection</h3>
            <button class="test-button" onclick="enableProtection()">
                <i class="fas fa-shield-alt"></i> Enable Refresh Protection
            </button>
            <div id="protection-status" class="status info">Protection status will appear here...</div>
        </div>
        
        <div>
            <h3>Step 2: Test Normal Redirect (Should Show Warning)</h3>
            <button class="test-button" onclick="testNormalRedirect()">
                <i class="fas fa-external-link-alt"></i> Test Normal Redirect
            </button>
            <p><small>This should trigger a "Changes you made may not be saved" warning.</small></p>
        </div>
        
        <div>
            <h3>Step 3: Test Payment Completion Redirect (Should NOT Show Warning)</h3>
            <button class="test-button success" onclick="testPaymentRedirect()">
                <i class="fas fa-check-circle"></i> Test Payment Completion Redirect
            </button>
            <p><small>This should redirect without any warnings.</small></p>
        </div>
        
        <div>
            <h3>Test Log</h3>
            <div id="test-log" class="log">Test log will appear here...\n</div>
            <button class="test-button" onclick="clearLog()">
                <i class="fas fa-trash"></i> Clear Log
            </button>
        </div>
    </div>
    
    <!-- Load refresh protection scripts -->
    <script src="refresh-protection.js"></script>
    
    <script>
        let protectionEnabled = false;
        
        function log(message) {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[Payment Redirect Test] ${message}`);
        }
        
        function updateProtectionStatus() {
            const statusDiv = document.getElementById('protection-status');
            
            if (window.refreshProtectionInstance && window.refreshProtectionInstance.isProtectionEnabled()) {
                statusDiv.className = 'status success';
                statusDiv.innerHTML = '<i class="fas fa-shield-alt"></i> Refresh protection is ACTIVE';
                protectionEnabled = true;
            } else {
                statusDiv.className = 'status warning';
                statusDiv.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Refresh protection is INACTIVE';
                protectionEnabled = false;
            }
        }
        
        function enableProtection() {
            if (typeof RefreshProtection !== 'undefined') {
                window.refreshProtectionInstance = RefreshProtection.enablePaymentProtection({
                    message: 'TEST: Are you sure you want to leave this page? Your test progress will be lost.'
                });
                log('✅ Refresh protection enabled');
                updateProtectionStatus();
            } else {
                log('❌ RefreshProtection class not available');
            }
        }
        
        function testNormalRedirect() {
            if (!protectionEnabled) {
                log('⚠️ Please enable protection first');
                return;
            }
            
            log('🔄 Testing normal redirect (should show warning)...');
            
            setTimeout(() => {
                // This should trigger the beforeunload warning
                window.location.href = 'test-refresh-protection.html';
            }, 500);
        }
        
        function testPaymentRedirect() {
            if (!protectionEnabled) {
                log('⚠️ Please enable protection first');
                return;
            }
            
            log('💳 Simulating payment completion...');
            
            // Simulate payment completion
            if (typeof RefreshProtection !== 'undefined' && RefreshProtection.disableForPaymentCompletion) {
                RefreshProtection.disableForPaymentCompletion();
                log('✅ Payment completion protection disabled');
            } else {
                log('❌ Payment completion method not available');
                return;
            }
            
            log('🔄 Testing payment redirect (should NOT show warning)...');
            
            setTimeout(() => {
                // This should NOT trigger any warnings
                window.location.href = 'test-refresh-protection.html?payment_completed=true';
            }, 1000);
        }
        
        function clearLog() {
            document.getElementById('test-log').textContent = 'Test log cleared...\n';
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🧪 Payment redirect test page loaded');
            updateProtectionStatus();
            
            // Check if we came from a payment completion test
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('payment_completed') === 'true') {
                log('✅ Successfully redirected after payment completion (no warning shown)');
                
                // Show success message
                const successDiv = document.createElement('div');
                successDiv.className = 'status success';
                successDiv.innerHTML = '<i class="fas fa-check-circle"></i> <strong>SUCCESS!</strong> Payment completion redirect worked without warnings.';
                document.querySelector('.test-container').insertBefore(successDiv, document.querySelector('.test-container').firstChild.nextSibling);
            }
        });
        
        // Monitor beforeunload events for testing
        window.addEventListener('beforeunload', function(e) {
            log('⚠️ beforeunload event triggered');
            
            // Check if this is during a payment completion test
            if (window.paymentCompleted || window.paymentInProgress) {
                log('✅ Payment flags detected - allowing navigation');
                return;
            }
            
            log('🛡️ Protection active - showing warning');
        });
    </script>
</body>
</html>
