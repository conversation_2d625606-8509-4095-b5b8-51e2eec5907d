<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Refresh Protection Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .test-button.danger {
            background: #dc3545;
        }
        
        .test-button.danger:hover {
            background: #c82333;
        }
        
        .test-button.success {
            background: #28a745;
        }
        
        .test-button.success:hover {
            background: #218838;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .keyboard-shortcuts {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .keyboard-shortcuts code {
            background: #e9ecef;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-shield-alt"></i> Refresh Protection Test Page</h1>
        <p>This page tests the refresh protection mechanisms for the payment page.</p>
        
        <div class="test-section">
            <h3><i class="fas fa-info-circle"></i> Protection Status</h3>
            <div id="protection-status" class="status info">
                <i class="fas fa-spinner fa-spin"></i> Checking protection status...
            </div>
            <button class="test-button" onclick="checkProtectionStatus()">
                <i class="fas fa-sync"></i> Refresh Status
            </button>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-keyboard"></i> Keyboard Shortcuts to Test</h3>
            <div class="keyboard-shortcuts">
                <p>Try these keyboard shortcuts - they should be blocked:</p>
                <ul>
                    <li><code>F5</code> - Standard refresh</li>
                    <li><code>Ctrl+R</code> (or <code>Cmd+R</code> on Mac) - Refresh</li>
                    <li><code>Ctrl+F5</code> - Hard refresh</li>
                    <li><code>Ctrl+Shift+R</code> - Hard refresh alternative</li>
                </ul>
                <p>These should show a confirmation dialog:</p>
                <ul>
                    <li><code>Ctrl+W</code> - Close tab (during simulated payment)</li>
                    <li><code>Alt+F4</code> - Close window (during simulated payment)</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-mouse-pointer"></i> Browser Actions to Test</h3>
            <p>Try these browser actions - they should show confirmation dialogs:</p>
            <ul>
                <li>Click the browser's refresh button</li>
                <li>Click the browser's back button</li>
                <li>Try to close the tab/window</li>
                <li>Right-click in the payment area (should be disabled)</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-credit-card"></i> Payment Simulation</h3>
            <p>Simulate payment process to test enhanced protection:</p>
            <button class="test-button success" onclick="simulatePaymentStart()">
                <i class="fas fa-play"></i> Start Payment Simulation
            </button>
            <button class="test-button" onclick="simulatePaymentComplete()">
                <i class="fas fa-check"></i> Complete Payment Simulation
            </button>
            <div id="payment-status" class="status info" style="display: none;">
                Payment simulation status will appear here...
            </div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-cogs"></i> Protection Controls</h3>
            <button class="test-button" onclick="enableProtection()">
                <i class="fas fa-shield-alt"></i> Enable Protection
            </button>
            <button class="test-button danger" onclick="disableProtection()">
                <i class="fas fa-shield-alt"></i> Disable Protection
            </button>
            <button class="test-button" onclick="allowNavigation()">
                <i class="fas fa-unlock"></i> Allow Navigation (5s)
            </button>
        </div>
        
        <!-- Simulated payment form for testing -->
        <form id="payment-form" style="display: none;">
            <input type="hidden" name="test" value="1">
        </form>
        
        <!-- Simulated payment container for context menu testing -->
        <div class="payment-container" style="display: none;"></div>
    </div>
    
    <!-- Load refresh protection scripts -->
    <script src="refresh-protection.js"></script>
    
    <script>
        let paymentSimulationActive = false;
        
        // Initialize protection when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Force enable payment protection for testing
            if (typeof RefreshProtection !== 'undefined') {
                window.refreshProtectionInstance = RefreshProtection.enablePaymentProtection({
                    message: 'TEST: Are you sure you want to refresh this page? Your payment process will be interrupted.'
                });
                console.log('🧪 Test page: Payment protection enabled');
            }
            
            // Update status
            setTimeout(checkProtectionStatus, 500);
        });
        
        function checkProtectionStatus() {
            const statusDiv = document.getElementById('protection-status');
            
            if (window.refreshProtectionInstance && window.refreshProtectionInstance.isProtectionEnabled()) {
                statusDiv.className = 'status success';
                statusDiv.innerHTML = '<i class="fas fa-shield-alt"></i> Refresh protection is ACTIVE';
            } else {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Refresh protection is INACTIVE';
            }
        }
        
        function enableProtection() {
            if (typeof RefreshProtection !== 'undefined') {
                window.refreshProtectionInstance = RefreshProtection.enablePaymentProtection({
                    message: 'TEST: Are you sure you want to refresh this page? Your payment process will be interrupted.'
                });
                console.log('🧪 Protection enabled manually');
                checkProtectionStatus();
            }
        }
        
        function disableProtection() {
            if (window.refreshProtectionInstance) {
                window.refreshProtectionInstance.disable();
                window.refreshProtectionInstance = null;
                console.log('🧪 Protection disabled manually');
                checkProtectionStatus();
            }
        }
        
        function allowNavigation() {
            if (window.refreshProtectionInstance) {
                window.refreshProtectionInstance.allowNavigation(5000);
                console.log('🧪 Navigation allowed for 5 seconds');
                
                const statusDiv = document.getElementById('protection-status');
                statusDiv.className = 'status info';
                statusDiv.innerHTML = '<i class="fas fa-unlock"></i> Navigation allowed for 5 seconds...';
                
                setTimeout(checkProtectionStatus, 5500);
            }
        }
        
        function simulatePaymentStart() {
            paymentSimulationActive = true;
            const statusDiv = document.getElementById('payment-status');
            statusDiv.style.display = 'block';
            statusDiv.className = 'status info';
            statusDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Payment simulation ACTIVE - try keyboard shortcuts now';
            
            // Trigger payment form submission event
            const form = document.getElementById('payment-form');
            const event = new Event('submit');
            form.dispatchEvent(event);
            
            console.log('🧪 Payment simulation started');
        }
        
        function simulatePaymentComplete() {
            paymentSimulationActive = false;
            const statusDiv = document.getElementById('payment-status');
            statusDiv.className = 'status success';
            statusDiv.innerHTML = '<i class="fas fa-check"></i> Payment simulation COMPLETED';
            
            if (window.refreshProtectionInstance) {
                window.refreshProtectionInstance.allowNavigation(10000);
            }
            
            console.log('🧪 Payment simulation completed');
        }
        
        // Monitor for beforeunload events during payment simulation
        window.addEventListener('beforeunload', function(e) {
            if (paymentSimulationActive) {
                const message = 'TEST: Payment simulation is active. Leaving this page will interrupt the test.';
                e.preventDefault();
                e.returnValue = message;
                return message;
            }
        });
    </script>
</body>
</html>
